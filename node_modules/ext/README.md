[![Build status][build-image]][build-url]
[![npm version][npm-image]][npm-url]

# ext

_(Previously known as `es5-ext`)_

## JavaScript language extensions (with respect to evolving standard)

Non-standard or soon to be standard language utilities in a future proof, non-invasive form.

Doesn't enforce transpilation step. Where it's applicable utilities/extensions are safe to use in all ES3+ implementations.

### Installation

```bash
npm install ext
```

### Utilities

- [`globalThis`](docs/global-this.md)
- `Function`
  - [`identity`](docs/function/identity.md)
- `Math`
  - [`ceil10`](docs/math/ceil-10.md)
  - [`floor10`](docs/math/floor-10.md)
  - [`round10`](docs/math/round-10.md)
- `Object`
  - [`clear`](docs/object/clear.md)
  - [`entries`](docs/object/entries.md)
- `Promise`
  - [`limit`](docs/promise/limit.md)
- `String`
  - [`random`](docs/string/random.md)
- `String.prototype`
  - [`campelToHyphen`](docs/string_/camel-to-hyphen.md)
  - [`capitalize`](docs/string_/capitalize.md)
  - [`includes`](docs/string_/includes.md)
- `Thenable.prototype`
  - [`finally`](docs/thenable_/finally.md)

[build-image]: https://github.com/medikoo/es5-ext/workflows/Integrate%20[ext]/badge.svg
[build-url]: https://github.com/medikoo/es5-ext/actions?query=workflow%3AIntegrate%20[ext]
[npm-image]: https://img.shields.io/npm/v/ext.svg
[npm-url]: https://www.npmjs.com/package/ext
