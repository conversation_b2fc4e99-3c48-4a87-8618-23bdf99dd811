# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [1.7.0](https://github.com/medikoo/es5-ext/compare/v1.6.0...v1.7.0) (2022-08-31)

### Features

- `string.camelToHyphen` method ([b8ea4ab](https://github.com/medikoo/es5-ext/commit/b8ea4ab6c8b920ecdff224f9c92092e1c7f8cdfc))
- `string.capitalize` method ([32e7360](https://github.com/medikoo/es5-ext/commit/32e736034bd27ed25d4566b22aa93aa66c7901cf))

## [1.6.0](https://github.com/medikoo/es5-ext/compare/v1.5.0...v1.6.0) (2021-09-24)

### Features

- `Object.clear` util ([a955da4](https://github.com/medikoo/es5-ext/commit/a955da41e65a25ad87a46234bae065f096abd1d2))

### Bug Fixes

- Fix `Object.entries` to not return non enumerable properties ([44fb872](https://github.com/medikoo/es5-ext/commit/44fb87266617378d2f47a1a5baad6280bf6298a8))

## [1.5.0](https://github.com/medikoo/es5-ext/compare/v1.3.0...v1.5.0) (2021-08-23)

### Features

- `Promise.limit` ([060a05d](https://github.com/medikoo/es5-ext/commit/060a05d4751cd291c6dd7641f5a73ba9338ea7ab))
- `String.prototype.includes` ([ceebe8d](https://github.com/medikoo/es5-ext/commit/ceebe8dfd6f479d6a7e7b6cd79369291869ee2dd))
- `charset` option for `String.random` ([2a20eeb](https://github.com/medikoo/es5-ext/commit/2a20eebc5ae784e5c1aacd2c54433fe92a9464c9))

## [1.4.0](https://github.com///compare/v1.3.0...v1.4.0) (2019-11-29)

### Features

- `charset` option for `String.random` ([2a20eeb](https://github.com///commit/2a20eebc5ae784e5c1aacd2c54433fe92a9464c9))
- `String.prototype.includes` implementation ([ceebe8d](https://github.com///commit/ceebe8dfd6f479d6a7e7b6cd79369291869ee2dd))

## [1.3.0](https://github.com///compare/v1.2.1...v1.3.0) (2019-11-28)

### Features

- `String.random` util ([5b5860a](https://github.com///commit/5b5860ac545b05f00527e00295fdb4f97e4a4e5b))

### [1.2.1](https://github.com///compare/v1.2.0...v1.2.1) (2019-11-26)

## [1.2.0](https://github.com/medikoo/ext/compare/v1.1.2...v1.2.0) (2019-11-07)

### Features

- ceil10, floor10 and round10 for Math ([6a2bc4b](https://github.com/medikoo/ext/commit/6a2bc4b))

### [1.1.2](https://github.com/medikoo/ext/compare/v1.1.1...v1.1.2) (2019-10-29)

### Bug Fixes

- Improve globalThis detection ([470862d](https://github.com/medikoo/ext/commit/470862d))

### [1.1.1](https://github.com/medikoo/ext/compare/v1.1.0...v1.1.1) (2019-10-29)

### Bug Fixes

- Provide naive fallback for sealed Object.prototype case ([a8d528b](https://github.com/medikoo/ext/commit/a8d528b))
- Workaournd Safari incompatibility case ([0b051e6](https://github.com/medikoo/ext/commit/0b051e6))

## [1.1.0](https://github.com/medikoo/ext/compare/v1.0.3...v1.1.0) (2019-10-21)

### Features

- Object.entries implementation ([cf51e45](https://github.com/medikoo/ext/commit/cf51e45))

### [1.0.3](https://github.com/medikoo/ext/compare/v1.0.1...v1.0.3) (2019-07-03)

Remove obsolete files from publication

### [1.0.2](https://github.com/medikoo/ext/compare/v1.0.1...v1.0.2) (2019-07-03)

(no changes)

### [1.0.1](https://github.com/medikoo/ext/compare/v1.0.0...v1.0.1) (2019-07-03)

Prettify

## 1.0.0 (2019-07-03)

### Features

- `function/identity` (adapted from `es5-ext`) ([f0102af](https://github.com/medikoo/ext/commit/f0102af))
- `thenable/finally` (adapted from `es5-ext`) ([a8494ac](https://github.com/medikoo/ext/commit/a8494ac))
- `global-this/is-implemented` ([3a80904](https://github.com/medikoo/ext/commit/3a80904))
- `globalThis` (mostly adapted from `es5-ext`) ([6559bd3](https://github.com/medikoo/ext/commit/6559bd3))
