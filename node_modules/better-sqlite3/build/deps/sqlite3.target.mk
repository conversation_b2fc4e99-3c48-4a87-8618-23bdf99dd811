# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := sqlite3
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=sqlite3' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DHAVE_INT16_T=1' \
	'-DHAVE_INT32_T=1' \
	'-DHAVE_INT8_T=1' \
	'-DHAVE_STDINT_H=1' \
	'-DHAVE_UINT16_T=1' \
	'-DHAVE_UINT32_T=1' \
	'-DHAVE_UINT8_T=1' \
	'-DHAVE_USLEEP=1' \
	'-DS<PERSON>ITE_DEFAULT_CACHE_SIZE=-16000' \
	'-DSQLITE_DEFAULT_FOREIGN_KEYS=1' \
	'-DSQLITE_DEFAULT_MEMSTATUS=0' \
	'-DSQLITE_DEFAULT_WAL_SYNCHRONOUS=1' \
	'-DSQLITE_DQS=0' \
	'-DSQLITE_ENABLE_COLUMN_METADATA' \
	'-DSQLITE_ENABLE_DESERIALIZE' \
	'-DSQLITE_ENABLE_FTS3' \
	'-DSQLITE_ENABLE_FTS3_PARENTHESIS' \
	'-DSQLITE_ENABLE_FTS4' \
	'-DSQLITE_ENABLE_FTS5' \
	'-DSQLITE_ENABLE_GEOPOLY' \
	'-DSQLITE_ENABLE_JSON1' \
	'-DSQLITE_ENABLE_MATH_FUNCTIONS' \
	'-DSQLITE_ENABLE_RTREE' \
	'-DSQLITE_ENABLE_STAT4' \
	'-DSQLITE_ENABLE_UPDATE_DELETE_LIMIT' \
	'-DSQLITE_LIKE_DOESNT_MATCH_BLOBS' \
	'-DSQLITE_OMIT_DEPRECATED' \
	'-DSQLITE_OMIT_PROGRESS_CALLBACK' \
	'-DSQLITE_OMIT_SHARED_CACHE' \
	'-DSQLITE_OMIT_TCL_VARIABLE' \
	'-DSQLITE_SOUNDEX' \
	'-DSQLITE_THREADSAFE=2' \
	'-DSQLITE_TRACE_SIZE_LIMIT=32' \
	'-DSQLITE_USE_URI=0' \
	'-DDEBUG' \
	'-D_DEBUG' \
	'-DSQLITE_DEBUG' \
	'-DSQLITE_MEMDEBUG' \
	'-DSQLITE_ENABLE_API_ARMOR' \
	'-DSQLITE_WIN32_MALLOC_VALIDATE'

# Flags passed to all source files.
CFLAGS_Debug := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-std=c99 \
	-w \
	-m64 \
	-g \
	-O0 \
	-O0

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-fno-rtti \
	-fno-exceptions \
	-fno-strict-aliasing \
	-std=gnu++17

INCS_Debug := \
	-I/home/<USER>/.cache/node-gyp/22.13.1/include/node \
	-I/home/<USER>/.cache/node-gyp/22.13.1/src \
	-I/home/<USER>/.cache/node-gyp/22.13.1/deps/openssl/config \
	-I/home/<USER>/.cache/node-gyp/22.13.1/deps/openssl/openssl/include \
	-I/home/<USER>/.cache/node-gyp/22.13.1/deps/uv/include \
	-I/home/<USER>/.cache/node-gyp/22.13.1/deps/zlib \
	-I/home/<USER>/.cache/node-gyp/22.13.1/deps/v8/include \
	-I$(obj)/gen/sqlite3

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=sqlite3' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DHAVE_INT16_T=1' \
	'-DHAVE_INT32_T=1' \
	'-DHAVE_INT8_T=1' \
	'-DHAVE_STDINT_H=1' \
	'-DHAVE_UINT16_T=1' \
	'-DHAVE_UINT32_T=1' \
	'-DHAVE_UINT8_T=1' \
	'-DHAVE_USLEEP=1' \
	'-DSQLITE_DEFAULT_CACHE_SIZE=-16000' \
	'-DSQLITE_DEFAULT_FOREIGN_KEYS=1' \
	'-DSQLITE_DEFAULT_MEMSTATUS=0' \
	'-DSQLITE_DEFAULT_WAL_SYNCHRONOUS=1' \
	'-DSQLITE_DQS=0' \
	'-DSQLITE_ENABLE_COLUMN_METADATA' \
	'-DSQLITE_ENABLE_DESERIALIZE' \
	'-DSQLITE_ENABLE_FTS3' \
	'-DSQLITE_ENABLE_FTS3_PARENTHESIS' \
	'-DSQLITE_ENABLE_FTS4' \
	'-DSQLITE_ENABLE_FTS5' \
	'-DSQLITE_ENABLE_GEOPOLY' \
	'-DSQLITE_ENABLE_JSON1' \
	'-DSQLITE_ENABLE_MATH_FUNCTIONS' \
	'-DSQLITE_ENABLE_RTREE' \
	'-DSQLITE_ENABLE_STAT4' \
	'-DSQLITE_ENABLE_UPDATE_DELETE_LIMIT' \
	'-DSQLITE_LIKE_DOESNT_MATCH_BLOBS' \
	'-DSQLITE_OMIT_DEPRECATED' \
	'-DSQLITE_OMIT_PROGRESS_CALLBACK' \
	'-DSQLITE_OMIT_SHARED_CACHE' \
	'-DSQLITE_OMIT_TCL_VARIABLE' \
	'-DSQLITE_SOUNDEX' \
	'-DSQLITE_THREADSAFE=2' \
	'-DSQLITE_TRACE_SIZE_LIMIT=32' \
	'-DSQLITE_USE_URI=0' \
	'-DNDEBUG'

# Flags passed to all source files.
CFLAGS_Release := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-std=c99 \
	-w \
	-m64 \
	-O3 \
	-O3 \
	-fno-omit-frame-pointer

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-fno-rtti \
	-fno-exceptions \
	-fno-strict-aliasing \
	-std=gnu++17

INCS_Release := \
	-I/home/<USER>/.cache/node-gyp/22.13.1/include/node \
	-I/home/<USER>/.cache/node-gyp/22.13.1/src \
	-I/home/<USER>/.cache/node-gyp/22.13.1/deps/openssl/config \
	-I/home/<USER>/.cache/node-gyp/22.13.1/deps/openssl/openssl/include \
	-I/home/<USER>/.cache/node-gyp/22.13.1/deps/uv/include \
	-I/home/<USER>/.cache/node-gyp/22.13.1/deps/zlib \
	-I/home/<USER>/.cache/node-gyp/22.13.1/deps/v8/include \
	-I$(obj)/gen/sqlite3

OBJS := \
	$(obj).target/$(TARGET)/gen/sqlite3/sqlite3.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# Make sure our dependencies are built before any of us.
$(OBJS): | $(obj).target/deps/locate_sqlite3.stamp

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-pthread \
	-rdynamic \
	-m64

LDFLAGS_Release := \
	-pthread \
	-rdynamic \
	-m64

LIBS :=

$(obj).target/deps/sqlite3.a: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(obj).target/deps/sqlite3.a: LIBS := $(LIBS)
$(obj).target/deps/sqlite3.a: TOOLSET := $(TOOLSET)
$(obj).target/deps/sqlite3.a: $(OBJS)
	$(call create_archive,$@,$^)

# Add target alias
.PHONY: sqlite3
sqlite3: $(obj).target/deps/sqlite3.a

# Add target alias to "all" target.
.PHONY: all
all: sqlite3

# Add target alias
.PHONY: sqlite3
sqlite3: $(builddir)/sqlite3.a

# Copy this to the static library output path.
$(builddir)/sqlite3.a: TOOLSET := $(TOOLSET)
$(builddir)/sqlite3.a: $(obj).target/deps/sqlite3.a FORCE_DO_CMD
	$(call do_cmd,copy)

all_deps += $(builddir)/sqlite3.a
# Short alias for building this static library.
.PHONY: sqlite3.a
sqlite3.a: $(obj).target/deps/sqlite3.a $(builddir)/sqlite3.a

# Add static library to "all" target.
.PHONY: all
all: $(builddir)/sqlite3.a

