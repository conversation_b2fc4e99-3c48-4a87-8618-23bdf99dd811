/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @providesModule equalsSet
 * @flow
 * @typechecks
 */
'use strict';

var everySet = require("./everySet");
/**
 * Checks if two sets are equal
 */


function equalsSet<T>(one: Set<T>, two: Set<T>): boolean {
  if (one.size !== two.size) {
    return false;
  }

  return everySet(one, value => two.has(value));
}

module.exports = equalsSet;