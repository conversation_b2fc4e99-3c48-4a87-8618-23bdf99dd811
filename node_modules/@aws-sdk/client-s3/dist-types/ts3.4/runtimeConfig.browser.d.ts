import { FetchHttpHandler as RequestHandler } from "@smithy/fetch-http-handler";
import { S3ClientConfig } from "./S3Client";
export declare const getRuntimeConfig: (config: S3ClientConfig) => {
  runtime: string;
  defaultsMode: import("@smithy/types").Provider<
    import("@smithy/smithy-client").ResolvedDefaultsMode
  >;
  bodyLengthChecker: import("@smithy/types").BodyLengthCalculator;
  credentialDefaultProvider: (
    input: any
  ) => import("@smithy/types").AwsCredentialIdentityProvider;
  defaultUserAgentProvider: import("@smithy/types").Provider<
    import("@smithy/types").UserAgent
  >;
  eventStreamSerdeProvider: import("@smithy/types").EventStreamSerdeProvider;
  maxAttempts: number | import("@smithy/types").Provider<number>;
  md5: import("@smithy/types").HashConstructor;
  region: string | import("@smithy/types").Provider<any>;
  requestHandler:
    | import("@smithy/protocol-http").HttpHandler<any>
    | RequestHandler;
  retryMode: string | import("@smithy/types").Provider<string>;
  sha1: import("@smithy/types").HashConstructor;
  sha256: import("@smithy/types").HashConstructor;
  streamCollector: import("@smithy/types").StreamCollector;
  streamHasher:
    | import("@smithy/types").StreamHasher<import("stream").Readable>
    | import("@smithy/types").StreamHasher<Blob>;
  useDualstackEndpoint: boolean | import("@smithy/types").Provider<boolean>;
  useFipsEndpoint: boolean | import("@smithy/types").Provider<boolean>;
  apiVersion: string;
  urlParser: import("@smithy/types").UrlParser;
  base64Decoder: import("@smithy/types").Decoder;
  base64Encoder: (_input: string | Uint8Array) => string;
  utf8Decoder: import("@smithy/types").Decoder;
  utf8Encoder: (input: string | Uint8Array) => string;
  disableHostPrefix: boolean;
  serviceId: string;
  getAwsChunkedEncodingStream:
    | import("@smithy/types").GetAwsChunkedEncodingStream<any>
    | import("@smithy/types").GetAwsChunkedEncodingStream<
        import("stream").Readable
      >;
  logger: import("@smithy/types").Logger;
  extensions: import("./runtimeExtensions").RuntimeExtension[];
  signingEscapePath: boolean;
  useArnRegion: boolean | import("@smithy/types").Provider<boolean>;
  sdkStreamMixin: import("@smithy/types").SdkStreamMixinInjector;
  customUserAgent?: string | import("@smithy/types").UserAgent | undefined;
  retryStrategy?:
    | import("@smithy/types").RetryStrategy
    | import("@smithy/types").RetryStrategyV2
    | undefined;
  endpoint?:
    | ((
        | string
        | import("@smithy/types").Endpoint
        | import("@smithy/types").Provider<import("@smithy/types").Endpoint>
        | import("@smithy/types").EndpointV2
        | import("@smithy/types").Provider<import("@smithy/types").EndpointV2>
      ) &
        (
          | string
          | import("@smithy/types").Provider<string>
          | import("@smithy/types").Endpoint
          | import("@smithy/types").Provider<import("@smithy/types").Endpoint>
          | import("@smithy/types").EndpointV2
          | import("@smithy/types").Provider<import("@smithy/types").EndpointV2>
        ))
    | undefined;
  endpointProvider: (
    endpointParams: import("./endpoint/EndpointParameters").EndpointParameters,
    context?: {
      logger?: import("@smithy/types").Logger | undefined;
    }
  ) => import("@smithy/types").EndpointV2;
  tls?: boolean | undefined;
  httpAuthSchemes: import("@smithy/types").HttpAuthScheme[];
  httpAuthSchemeProvider: import("./auth/httpAuthSchemeProvider").S3HttpAuthSchemeProvider;
  credentials?:
    | import("@smithy/types").AwsCredentialIdentity
    | import("@smithy/types").AwsCredentialIdentityProvider
    | undefined;
  signer?:
    | import("@smithy/types").RequestSigner
    | ((
        authScheme?: import("@smithy/types").AuthScheme | undefined
      ) => Promise<import("@smithy/types").RequestSigner>)
    | undefined;
  systemClockOffset?: number | undefined;
  signingRegion?: string | undefined;
  signerConstructor:
    | (new (
        options: import("@smithy/signature-v4").SignatureV4Init &
          import("@smithy/signature-v4").SignatureV4CryptoInit
      ) => import("@smithy/types").RequestSigner)
    | typeof import("@aws-sdk/signature-v4-multi-region").SignatureV4MultiRegion;
  forcePathStyle?:
    | (boolean & (boolean | import("@smithy/types").Provider<boolean>))
    | undefined;
  useAccelerateEndpoint?:
    | (boolean & (boolean | import("@smithy/types").Provider<boolean>))
    | undefined;
  disableMultiregionAccessPoints?:
    | (boolean & (boolean | import("@smithy/types").Provider<boolean>))
    | undefined;
  followRegionRedirects?: boolean | undefined;
  s3ExpressIdentityProvider?:
    | import("@aws-sdk/middleware-sdk-s3").S3ExpressIdentityProvider
    | undefined;
  bucketEndpoint?: boolean | undefined;
  useGlobalEndpoint?:
    | boolean
    | import("@smithy/types").Provider<boolean>
    | undefined;
  disableS3ExpressSessionAuth?:
    | boolean
    | import("@smithy/types").Provider<boolean>
    | undefined;
};
