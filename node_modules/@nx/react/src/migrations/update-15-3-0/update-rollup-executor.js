"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateRollupExecutor = void 0;
const devkit_1 = require("@nx/devkit");
function updateRollupExecutor(tree) {
    const projects = (0, devkit_1.getProjects)(tree);
    for (const [name, project] of projects) {
        if (project.targets?.build?.executor === '@nrwl/web:rollup') {
            project.targets.build.executor = '@nrwl/rollup:rollup';
            (0, devkit_1.updateProjectConfiguration)(tree, name, project);
        }
    }
}
exports.updateRollupExecutor = updateRollupExecutor;
exports.default = updateRollupExecutor;
