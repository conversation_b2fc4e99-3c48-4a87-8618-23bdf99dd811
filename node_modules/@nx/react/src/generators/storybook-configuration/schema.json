{"$schema": "https://json-schema.org/schema", "cli": "nx", "$id": "NxReactStorybookConfigure", "title": "React Storybook Configure", "description": "Set up Storybook for a React app or library.", "type": "object", "properties": {"project": {"type": "string", "aliases": ["name", "projectName"], "description": "Project for which to generate Storybook configuration.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "For which project do you want to generate Storybook configuration?", "x-dropdown": "projects", "x-priority": "important"}, "interactionTests": {"type": "boolean", "description": "Set up Storybook interaction tests.", "x-prompt": "Do you want to set up Storybook interaction tests?", "x-priority": "important", "alias": ["configure<PERSON>est<PERSON><PERSON><PERSON>"], "default": true}, "configureCypress": {"type": "boolean", "description": "Run the cypress-configure generator.", "x-deprecated": "Use interactionTests instead. This option will be removed in v19."}, "generateStories": {"type": "boolean", "description": "Automatically generate `*.stories.ts` files for components declared in this project?", "x-prompt": "Automatically generate *.stories.ts files for components declared in this project?", "default": true, "x-priority": "important"}, "generateCypressSpecs": {"type": "boolean", "description": "Automatically generate test files in the Cypress E2E app generated by the `cypress-configure` generator.", "x-deprecated": "Use interactionTests instead. This option will be removed in v19."}, "configureStaticServe": {"type": "boolean", "description": "Specifies whether to configure a static file server target for serving storybook. Helpful for speeding up CI build/test times.", "x-prompt": "Configure a static file server for the storybook instance?", "default": true, "x-priority": "important"}, "cypressDirectory": {"type": "string", "description": "A directory where the Cypress project will be placed. Placed at the root by default.", "x-deprecated": "Use interactionTests instead. This option will be removed in v19."}, "js": {"type": "boolean", "description": "Generate JavaScript story files rather than TypeScript story files.", "default": false}, "tsConfiguration": {"type": "boolean", "description": "Configure your project with TypeScript. Generate main.ts and preview.ts files, instead of main.js and preview.js.", "default": true}, "linter": {"description": "The tool to use for running lint checks.", "type": "string", "enum": ["eslint"], "default": "eslint"}, "ignorePaths": {"type": "array", "description": "Paths to ignore when looking for components.", "items": {"type": "string", "description": "Path to ignore."}, "default": ["*.stories.ts,*.stories.tsx,*.stories.js,*.stories.jsx,*.stories.mdx"], "examples": ["apps/my-app/src/not-stories/**", "**/**/src/**/not-stories/**", "libs/my-lib/**/*.something.ts", "**/**/src/**/*.other.*", "libs/my-lib/src/not-stories/**,**/**/src/**/*.other.*,apps/my-app/**/*.something.ts"]}}, "required": ["project"], "examplesFile": "../../../docs/storybook-configuration-examples.md"}