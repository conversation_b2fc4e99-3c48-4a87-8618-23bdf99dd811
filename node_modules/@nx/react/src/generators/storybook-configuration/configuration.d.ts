import { StorybookConfigureSchema } from './schema';
import { Tree } from '@nx/devkit';
export declare function storybookConfigurationGenerator(host: Tree, schema: StorybookConfigureSchema): Promise<import("@nx/devkit").GeneratorCallback>;
export declare function storybookConfigurationGeneratorInternal(host: Tree, schema: StorybookConfigureSchema): Promise<import("@nx/devkit").GeneratorCallback>;
export default storybookConfigurationGenerator;
export declare function findWebpackConfig(tree: Tree, projectRoot: string): string | undefined;
