"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findWebpackConfig = exports.storybookConfigurationGeneratorInternal = exports.storybookConfigurationGenerator = void 0;
const stories_1 = require("../stories/stories");
const devkit_1 = require("@nx/devkit");
const versions_1 = require("../../utils/versions");
async function generateStories(host, schema) {
    // TODO(katerina): Nx 19 -> remove Cypress
    (0, devkit_1.ensurePackage)('@nx/cypress', versions_1.nxVersion);
    const { getE2eProjectName } = await Promise.resolve().then(() => require('@nx/cypress/src/utils/project-name'));
    const projectConfig = (0, devkit_1.readProjectConfiguration)(host, schema.project);
    const cypressProject = getE2eProjectName(schema.project, projectConfig.root, schema.cypressDirectory);
    await (0, stories_1.default)(host, {
        project: schema.project,
        generateCypressSpecs: schema.configureCypress && schema.generateCypressSpecs,
        js: schema.js,
        cypressProject,
        ignorePaths: schema.ignorePaths,
        skipFormat: true,
        interactionTests: schema.interactionTests ?? true,
    });
}
function storybookConfigurationGenerator(host, schema) {
    return storybookConfigurationGeneratorInternal(host, {
        addPlugin: false,
        ...schema,
    });
}
exports.storybookConfigurationGenerator = storybookConfigurationGenerator;
async function storybookConfigurationGeneratorInternal(host, schema) {
    const nxJson = (0, devkit_1.readNxJson)(host);
    const addPluginDefault = process.env.NX_ADD_PLUGINS !== 'false' &&
        nxJson.useInferencePlugins !== false;
    schema.addPlugin ??= addPluginDefault;
    const { configurationGenerator } = (0, devkit_1.ensurePackage)('@nx/storybook', versions_1.nxVersion);
    let uiFramework = '@storybook/react-vite';
    const projectConfig = (0, devkit_1.readProjectConfiguration)(host, schema.project);
    if (findWebpackConfig(host, projectConfig.root) ||
        projectConfig.targets['build']?.executor === '@nx/rollup:rollup' ||
        projectConfig.targets['build']?.executor === '@nrwl/rollup:rollup' ||
        projectConfig.targets['build']?.executor === '@nx/expo:build') {
        uiFramework = '@storybook/react-webpack5';
    }
    const installTask = await configurationGenerator(host, {
        project: schema.project,
        configureCypress: schema.configureCypress,
        js: schema.js,
        linter: schema.linter,
        cypressDirectory: schema.cypressDirectory,
        tsConfiguration: schema.tsConfiguration ?? true, // default is true
        interactionTests: schema.interactionTests ?? true, // default is true
        configureStaticServe: schema.configureStaticServe,
        uiFramework: uiFramework, // cannot import UiFramework type dynamically
        skipFormat: true,
        addPlugin: schema.addPlugin,
    });
    if (schema.generateStories) {
        await generateStories(host, schema);
    }
    await (0, devkit_1.formatFiles)(host);
    return installTask;
}
exports.storybookConfigurationGeneratorInternal = storybookConfigurationGeneratorInternal;
exports.default = storybookConfigurationGenerator;
function findWebpackConfig(tree, projectRoot) {
    const allowsExt = ['js', 'mjs', 'ts', 'cjs', 'mts', 'cts'];
    for (const ext of allowsExt) {
        const webpackConfigPath = (0, devkit_1.joinPathFragments)(projectRoot, `webpack.config.${ext}`);
        if (tree.exists(webpackConfigPath)) {
            return webpackConfigPath;
        }
    }
}
exports.findWebpackConfig = findWebpackConfig;
