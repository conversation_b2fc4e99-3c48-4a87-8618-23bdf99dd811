{"$schema": "https://json-schema.org/schema", "cli": "nx", "$id": "NxReactComponentStory", "title": "Create component story", "description": "Generate storybook story for a react component.", "type": "object", "properties": {"project": {"type": "string", "aliases": ["name", "projectName"], "description": "The project where to add the components.", "examples": ["shared-ui-component"], "$default": {"$source": "projectName", "index": 0}, "x-prompt": "What's name of the project where the component lives?", "x-priority": "important"}, "componentPath": {"type": "string", "description": "Relative path to the component file from the library root.", "examples": ["lib/components"], "x-prompt": "What's path of the component relative to the project's lib root?", "x-priority": "important"}, "skipFormat": {"description": "Skip formatting files.", "type": "boolean", "default": false, "x-priority": "internal"}, "interactionTests": {"type": "boolean", "description": "Set up Storybook interaction tests.", "default": true, "x-priority": "important"}}, "required": ["project", "componentPath"]}