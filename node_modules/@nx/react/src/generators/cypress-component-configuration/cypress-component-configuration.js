"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cypressComponentConfigGeneratorInternal = exports.cypressComponentConfigGenerator = void 0;
const devkit_1 = require("@nx/devkit");
const versions_1 = require("../../utils/versions");
const add_files_1 = require("./lib/add-files");
const ct_utils_1 = require("../../utils/ct-utils");
function cypressComponentConfigGenerator(tree, options) {
    return cypressComponentConfigGeneratorInternal(tree, {
        addPlugin: false,
        ...options,
    });
}
exports.cypressComponentConfigGenerator = cypressComponentConfigGenerator;
/**
 * This is for using cypresses own Component testing, if you want to use test
 * storybook components then use componentCypressGenerator instead.
 *
 */
async function cypressComponentConfigGeneratorInternal(tree, options) {
    const { componentConfigurationGenerator: baseCyCtConfig } = (0, devkit_1.ensurePackage)('@nx/cypress', versions_1.nxVersion);
    const nxJson = (0, devkit_1.readNxJson)(tree);
    const addPlugin = process.env.NX_ADD_PLUGINS !== 'false' &&
        nxJson.useInferencePlugins !== false;
    options.addPlugin ??= addPlugin;
    const projectConfig = (0, devkit_1.readProjectConfiguration)(tree, options.project);
    const installTask = await baseCyCtConfig(tree, {
        project: options.project,
        skipFormat: true,
        jsx: true,
        addPlugin: options.addPlugin,
    });
    const found = await (0, ct_utils_1.configureCypressCT)(tree, {
        project: options.project,
        buildTarget: options.buildTarget,
        bundler: options.bundler,
        validExecutorNames: new Set([
            '@nx/webpack:webpack',
            '@nx/vite:build',
            '@nrwl/webpack:webpack',
            '@nrwl/vite:build',
        ]),
    });
    await (0, add_files_1.addFiles)(tree, projectConfig, options, found);
    if (!options.skipFormat) {
        await (0, devkit_1.formatFiles)(tree);
    }
    return installTask;
}
exports.cypressComponentConfigGeneratorInternal = cypressComponentConfigGeneratorInternal;
exports.default = cypressComponentConfigGenerator;
