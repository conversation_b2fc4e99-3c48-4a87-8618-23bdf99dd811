{"$schema": "https://json-schema.org/schema", "cli": "nx", "$id": "NxReactCypressComponentTestConfiguration", "title": "Add Cypress component testing", "description": "Add a Cypress component testing configuration to an existing project.", "type": "object", "examples": [{"command": "nx g @nx/react:cypress-component-configuration --project=my-react-project", "description": "Add component testing to your react project"}, {"command": "nx g @nx/react:cypress-component-configuration --project=my-react-project --generate-tests", "description": "Add component testing to your react project and generate component tests for your existing components"}], "properties": {"project": {"type": "string", "description": "The name of the project to add cypress component testing configuration to", "x-dropdown": "projects", "x-prompt": "What project should we add Cypress component testing to?", "x-priority": "important"}, "generateTests": {"type": "boolean", "description": "Generate default component tests for existing components in the project", "x-prompt": "Automatically generate tests for components declared in this project?", "default": false, "x-priority": "important"}, "buildTarget": {"type": "string", "description": "A build target used to configure Cypress component testing in the format of `project:target[:configuration]`. The build target should be from a React app. If not provided we will try to infer it from your projects usage.", "pattern": "^[^:\\s]+:[^:\\s]+(:\\S+)?$"}, "skipFormat": {"type": "boolean", "description": "Skip formatting files", "default": false, "x-priority": "internal"}, "bundler": {"description": "The bundler to use for Cypress Component Testing.", "type": "string", "enum": ["vite", "webpack"], "hidden": true, "x-priority": "internal"}}, "required": ["project"], "examplesFile": "../../../docs/cypress-component-configuration-examples.md"}