"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupTailwindGenerator = void 0;
const devkit_1 = require("@nx/devkit");
const versions_1 = require("../../utils/versions");
const add_tailwind_style_imports_1 = require("./lib/add-tailwind-style-imports");
const update_project_1 = require("./lib/update-project");
const path_1 = require("path");
async function setupTailwindGenerator(tree, options) {
    const tasks = [];
    const project = (0, devkit_1.readProjectConfiguration)(tree, options.project);
    if (tree.exists((0, devkit_1.joinPathFragments)(project.root, 'postcss.config.js')) ||
        tree.exists((0, devkit_1.joinPathFragments)(project.root, 'tailwind.config.js'))) {
        devkit_1.logger.info(`Skipping setup since there are existing PostCSS or Tailwind configuration files. For manual setup instructions, see https://nx.dev/guides/using-tailwind-css-in-react.`);
        return;
    }
    (0, devkit_1.generateFiles)(tree, (0, path_1.join)(__dirname, './files'), project.root, {
        tmpl: '',
    });
    (0, add_tailwind_style_imports_1.addTailwindStyleImports)(tree, project, options);
    (0, update_project_1.updateProject)(tree, project, options);
    if (!options.skipPackageJson) {
        tasks.push((0, devkit_1.addDependenciesToPackageJson)(tree, {}, {
            autoprefixer: versions_1.autoprefixerVersion,
            postcss: versions_1.postcssVersion,
            tailwindcss: versions_1.tailwindcssVersion,
        }));
    }
    if (!options.skipFormat) {
        await (0, devkit_1.formatFiles)(tree);
    }
    return (0, devkit_1.runTasksInSerial)(...tasks);
}
exports.setupTailwindGenerator = setupTailwindGenerator;
exports.default = setupTailwindGenerator;
