{"$schema": "https://json-schema.org/schema", "cli": "nx", "$id": "NxReactTailwindSetupGenerator", "title": "Configures Tailwind CSS for an application or a buildable/publishable library.", "description": "Adds the Tailwind CSS configuration files for a given React project and installs, if needed, the packages required for Tailwind CSS to work.", "type": "object", "examples": [{"command": "nx g setup-tailwind --project=my-app", "description": "Initialize Tailwind configuration for the `my-app` project."}], "properties": {"project": {"type": "string", "description": "The name of the project to add the Tailwind CSS setup for.", "alias": "p", "$default": {"$source": "argv", "index": 0}, "x-dropdown": "projects", "x-prompt": "What project would you like to add the Tailwind CSS setup?", "x-priority": "important"}, "buildTarget": {"type": "string", "description": "The name of the target used to build the project. This option is not needed in most cases.", "default": "build", "x-priority": "important"}, "skipFormat": {"type": "boolean", "description": "Skips formatting the workspace after the generator completes.", "x-priority": "internal"}, "skipPackageJson": {"type": "boolean", "default": false, "description": "Do not add dependencies to `package.json`.", "x-priority": "internal"}}, "additionalProperties": false, "required": ["project"]}