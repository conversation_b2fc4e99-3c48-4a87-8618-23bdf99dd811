{"$schema": "https://json-schema.org/schema", "cli": "nx", "$id": "NxReactLibrary", "title": "Create a React Library", "description": "Create a React Library for an Nx workspace.", "type": "object", "examples": [{"command": "nx g lib mylib --directory=myapp", "description": "Generate `libs/myapp/mylib`"}, {"command": "nx g lib mylib --appProject=myapp", "description": "Generate a library with routes and add them to `myapp`"}], "properties": {"name": {"type": "string", "description": "Library name", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the library?", "pattern": "(?:^@[a-zA-Z0-9-*~][a-zA-Z0-9-*._~]*\\/[a-zA-Z0-9-~][a-zA-Z0-9-._~]*|^[a-zA-Z][^:]*)$", "x-priority": "important"}, "directory": {"type": "string", "description": "A directory where the lib is placed.", "alias": "dir", "x-priority": "important"}, "projectNameAndRootFormat": {"description": "Whether to generate the project name and root directory as provided (`as-provided`) or generate them composing their values and taking the configured layout into account (`derived`).", "type": "string", "enum": ["as-provided", "derived"]}, "style": {"description": "The file extension to be used for style files.", "type": "string", "default": "css", "alias": "s", "x-prompt": {"message": "Which stylesheet format would you like to use?", "type": "list", "items": [{"value": "css", "label": "CSS"}, {"value": "scss", "label": "SASS(.scss)       [ https://sass-lang.com          ]"}, {"value": "less", "label": "LESS              [ https://lesscss.org            ]"}, {"value": "styled-components", "label": "styled-components [ https://styled-components.com ]"}, {"value": "@emotion/styled", "label": "emotion           [ https://emotion.sh            ]"}, {"value": "styled-jsx", "label": "styled-jsx        [ https://www.npmjs.com/package/styled-jsx ]"}, {"value": "none", "label": "None"}]}}, "linter": {"description": "The tool to use for running lint checks.", "type": "string", "enum": ["eslint", "none"], "default": "eslint"}, "unitTestRunner": {"type": "string", "enum": ["jest", "vitest", "none"], "description": "Test runner to use for unit tests.", "x-prompt": "What unit test runner should be used?"}, "inSourceTests": {"type": "boolean", "default": false, "description": "When using Vitest, separate spec files will not be generated and instead will be included within the source files."}, "tags": {"type": "string", "description": "Add tags to the library (used for linting).", "alias": "t"}, "skipFormat": {"description": "Skip formatting files.", "type": "boolean", "default": false, "x-priority": "internal"}, "skipTsConfig": {"type": "boolean", "default": false, "description": "Do not update `tsconfig.json` for development experience.", "x-priority": "internal"}, "pascalCaseFiles": {"type": "boolean", "description": "Use pascal case component file name (e.g. `App.tsx`).", "alias": "P", "default": false}, "routing": {"type": "boolean", "description": "Generate library with routes."}, "appProject": {"type": "string", "description": "The application project to add the library route to.", "alias": "a"}, "publishable": {"type": "boolean", "description": "Create a publishable library."}, "buildable": {"type": "boolean", "default": false, "description": "Generate a buildable library that uses rollup to bundle.", "x-deprecated": "Use the `bundler` option for greater control (none, vite, rollup)."}, "importPath": {"type": "string", "description": "The library name used to import it, like `@myorg/my-awesome-lib`."}, "component": {"type": "boolean", "description": "Generate a default component.", "default": true}, "js": {"type": "boolean", "description": "Generate JavaScript files rather than TypeScript files.", "default": false}, "globalCss": {"type": "boolean", "description": "When `true`, the stylesheet is generated using global CSS instead of CSS modules (e.g. file is `*.css` rather than `*.module.css`).", "default": false}, "strict": {"type": "boolean", "description": "Whether to enable tsconfig strict mode or not.", "default": true}, "setParserOptionsProject": {"type": "boolean", "description": "Whether or not to configure the ESLint `parserOptions.project` option. We do not do this by default for lint performance reasons.", "default": false}, "bundler": {"type": "string", "description": "The bundler to use. Choosing 'none' means this library is not buildable.", "enum": ["none", "vite", "rollup"], "default": "none", "x-prompt": "Which bundler would you like to use to build the library? Choose 'none' to skip build setup.", "x-priority": "important"}, "compiler": {"type": "string", "enum": ["babel", "swc"], "default": "babel", "description": "Which compiler to use."}, "skipPackageJson": {"description": "Do not add dependencies to `package.json`.", "type": "boolean", "default": false, "x-priority": "internal"}, "minimal": {"description": "Create a React library with a minimal setup, no separate test files.", "type": "boolean", "default": false}, "simpleName": {"description": "Don't include the directory in the name of the module of the library.", "type": "boolean", "default": false}}, "required": ["name"]}