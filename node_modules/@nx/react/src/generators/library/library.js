"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.libraryGeneratorInternal = exports.libraryGenerator = void 0;
const path_1 = require("path");
const devkit_1 = require("@nx/devkit");
const artifact_name_and_directory_utils_1 = require("@nx/devkit/src/generators/artifact-name-and-directory-utils");
const log_show_project_command_1 = require("@nx/devkit/src/utils/log-show-project-command");
const js_1 = require("@nx/js");
const versions_1 = require("../../utils/versions");
const maybe_js_1 = require("../../utils/maybe-js");
const component_1 = require("../component/component");
const init_1 = require("../init/init");
const jest_utils_1 = require("../../utils/jest-utils");
const normalize_options_1 = require("./lib/normalize-options");
const add_rollup_build_target_1 = require("./lib/add-rollup-build-target");
const add_linting_1 = require("./lib/add-linting");
const update_app_routes_1 = require("./lib/update-app-routes");
const create_files_1 = require("./lib/create-files");
const create_ts_config_1 = require("../../utils/create-ts-config");
const install_common_dependencies_1 = require("./lib/install-common-dependencies");
const set_defaults_1 = require("./lib/set-defaults");
async function libraryGenerator(host, schema) {
    return await libraryGeneratorInternal(host, {
        addPlugin: false,
        projectNameAndRootFormat: 'derived',
        ...schema,
    });
}
exports.libraryGenerator = libraryGenerator;
async function libraryGeneratorInternal(host, schema) {
    const tasks = [];
    const options = await (0, normalize_options_1.normalizeOptions)(host, schema);
    if (options.publishable === true && !schema.importPath) {
        throw new Error(`For publishable libs you have to provide a proper "--importPath" which needs to be a valid npm package name (e.g. my-awesome-lib or @myorg/my-lib)`);
    }
    if (!options.component) {
        options.style = 'none';
    }
    const jsInitTask = await (0, js_1.initGenerator)(host, {
        ...schema,
        skipFormat: true,
    });
    tasks.push(jsInitTask);
    const initTask = await (0, init_1.default)(host, {
        ...options,
        skipFormat: true,
    });
    tasks.push(initTask);
    (0, devkit_1.addProjectConfiguration)(host, options.name, {
        root: options.projectRoot,
        sourceRoot: (0, devkit_1.joinPathFragments)(options.projectRoot, 'src'),
        projectType: 'library',
        tags: options.parsedTags,
        targets: {},
    });
    const lintTask = await (0, add_linting_1.addLinting)(host, options);
    tasks.push(lintTask);
    (0, create_files_1.createFiles)(host, options);
    // Set up build target
    if (options.buildable && options.bundler === 'vite') {
        const { viteConfigurationGenerator, createOrEditViteConfig } = (0, devkit_1.ensurePackage)('@nx/vite', versions_1.nxVersion);
        const viteTask = await viteConfigurationGenerator(host, {
            uiFramework: 'react',
            project: options.name,
            newProject: true,
            includeLib: true,
            inSourceTests: options.inSourceTests,
            includeVitest: options.unitTestRunner === 'vitest',
            compiler: options.compiler,
            skipFormat: true,
            testEnvironment: 'jsdom',
            addPlugin: options.addPlugin,
        });
        tasks.push(viteTask);
        createOrEditViteConfig(host, {
            project: options.name,
            includeLib: true,
            includeVitest: options.unitTestRunner === 'vitest',
            inSourceTests: options.inSourceTests,
            rollupOptionsExternal: [
                "'react'",
                "'react-dom'",
                "'react/jsx-runtime'",
            ],
            imports: [
                options.compiler === 'swc'
                    ? `import react from '@vitejs/plugin-react-swc'`
                    : `import react from '@vitejs/plugin-react'`,
            ],
            plugins: ['react()'],
        }, false);
    }
    else if (options.buildable && options.bundler === 'rollup') {
        const rollupTask = await (0, add_rollup_build_target_1.addRollupBuildTarget)(host, options);
        tasks.push(rollupTask);
    }
    // Set up test target
    if (options.unitTestRunner === 'jest') {
        const { configurationGenerator } = (0, devkit_1.ensurePackage)('@nx/jest', versions_1.nxVersion);
        const jestTask = await configurationGenerator(host, {
            ...options,
            project: options.name,
            setupFile: 'none',
            supportTsx: true,
            skipSerializers: true,
            compiler: options.compiler,
            skipFormat: true,
        });
        tasks.push(jestTask);
        const jestConfigPath = (0, devkit_1.joinPathFragments)(options.projectRoot, options.js ? 'jest.config.js' : 'jest.config.ts');
        if (options.compiler === 'babel' && host.exists(jestConfigPath)) {
            const updatedContent = (0, jest_utils_1.updateJestConfigContent)(host.read(jestConfigPath, 'utf-8'));
            host.write(jestConfigPath, updatedContent);
        }
    }
    else if (options.unitTestRunner === 'vitest' &&
        options.bundler !== 'vite' // tests are already configured if bundler is vite
    ) {
        const { vitestGenerator, createOrEditViteConfig } = (0, devkit_1.ensurePackage)('@nx/vite', versions_1.nxVersion);
        const vitestTask = await vitestGenerator(host, {
            uiFramework: 'react',
            project: options.name,
            coverageProvider: 'v8',
            inSourceTests: options.inSourceTests,
            skipFormat: true,
            testEnvironment: 'jsdom',
            addPlugin: options.addPlugin,
        });
        tasks.push(vitestTask);
        createOrEditViteConfig(host, {
            project: options.name,
            includeLib: true,
            includeVitest: true,
            inSourceTests: options.inSourceTests,
            rollupOptionsExternal: [
                "'react'",
                "'react-dom'",
                "'react/jsx-runtime'",
            ],
            imports: [`import react from '@vitejs/plugin-react'`],
            plugins: ['react()'],
        }, true);
    }
    if (options.component) {
        const relativeCwd = (0, artifact_name_and_directory_utils_1.getRelativeCwd)();
        const name = (0, devkit_1.joinPathFragments)(options.projectRoot, 'src/lib', options.fileName);
        const componentTask = await (0, component_1.default)(host, {
            nameAndDirectoryFormat: 'as-provided',
            name: relativeCwd ? (0, path_1.relative)(relativeCwd, name) : name,
            project: options.name,
            flat: true,
            style: options.style,
            skipTests: options.unitTestRunner === 'none' ||
                (options.unitTestRunner === 'vitest' && options.inSourceTests == true),
            export: true,
            routing: options.routing,
            js: options.js,
            pascalCaseFiles: options.pascalCaseFiles,
            inSourceTests: options.inSourceTests,
            skipFormat: true,
            globalCss: options.globalCss,
        });
        tasks.push(componentTask);
    }
    if (options.publishable || options.buildable) {
        (0, devkit_1.updateJson)(host, `${options.projectRoot}/package.json`, (json) => {
            json.name = options.importPath;
            return json;
        });
    }
    if (!options.skipPackageJson) {
        const installReactTask = (0, install_common_dependencies_1.installCommonDependencies)(host, options);
        tasks.push(installReactTask);
    }
    const routeTask = (0, update_app_routes_1.updateAppRoutes)(host, options);
    tasks.push(routeTask);
    (0, set_defaults_1.setDefaults)(host, options);
    (0, create_ts_config_1.extractTsConfigBase)(host);
    if (!options.skipTsConfig) {
        (0, js_1.addTsConfigPath)(host, options.importPath, [
            (0, maybe_js_1.maybeJs)(options, (0, devkit_1.joinPathFragments)(options.projectRoot, './src/index.ts')),
        ]);
    }
    if (!options.skipFormat) {
        await (0, devkit_1.formatFiles)(host);
    }
    tasks.push(() => {
        (0, log_show_project_command_1.logShowProjectCommand)(options.name);
    });
    return (0, devkit_1.runTasksInSerial)(...tasks);
}
exports.libraryGeneratorInternal = libraryGeneratorInternal;
exports.default = libraryGenerator;
