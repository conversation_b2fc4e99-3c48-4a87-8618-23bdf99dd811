{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "outDir": "<%= offsetFromRoot %>dist/out-tsc",
    "types": [
      "node",
      <% if (style === 'styled-jsx') { %>"@nx/react/typings/styled-jsx.d.ts",<% } %>
      "@nx/react/typings/cssmodule.d.ts",
      "@nx/react/typings/image.d.ts"
    ]
  },
  "exclude": ["**/*.spec.ts", "**/*.test.ts", "**/*.spec.tsx", "**/*.test.tsx", "**/*.spec.js", "**/*.test.js", "**/*.spec.jsx", "**/*.test.jsx"],
  "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"]
}
