import { GeneratorCallback, ProjectConfiguration, Tree } from '@nx/devkit';
export interface StorybookStoriesSchema {
    project: string;
    interactionTests?: boolean;
    js?: boolean;
    ignorePaths?: string[];
    skipFormat?: boolean;
    cypressProject?: string;
    generateCypressSpecs?: boolean;
}
export declare function projectRootPath(tree: Tree, config: ProjectConfiguration): Promise<string>;
export declare function containsComponentDeclaration(tree: Tree, componentPath: string): boolean;
export declare function createAllStories(tree: Tree, projectName: string, interactionTests: boolean, js: boolean, projects: Map<string, ProjectConfiguration>, projectConfiguration: ProjectConfiguration, generateCypressSpecs?: boolean, cypressProject?: string, ignorePaths?: string[]): Promise<void>;
export declare function storiesGenerator(host: Tree, schema: StorybookStoriesSchema): Promise<GeneratorCallback>;
export default storiesGenerator;
