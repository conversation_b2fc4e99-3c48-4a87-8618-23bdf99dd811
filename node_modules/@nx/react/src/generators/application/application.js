"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.applicationGeneratorInternal = exports.applicationGenerator = void 0;
const lint_1 = require("../../utils/lint");
const create_application_files_1 = require("./lib/create-application-files");
const update_jest_config_1 = require("./lib/update-jest-config");
const normalize_options_1 = require("./lib/normalize-options");
const add_project_1 = require("./lib/add-project");
const add_jest_1 = require("./lib/add-jest");
const add_routing_1 = require("./lib/add-routing");
const set_defaults_1 = require("./lib/set-defaults");
const add_styled_dependencies_1 = require("../../rules/add-styled-dependencies");
const devkit_1 = require("@nx/devkit");
const init_1 = require("../init/init");
const eslint_1 = require("@nx/eslint");
const versions_1 = require("../../utils/versions");
const maybe_js_1 = require("../../utils/maybe-js");
const install_common_dependencies_1 = require("./lib/install-common-dependencies");
const create_ts_config_1 = require("../../utils/create-ts-config");
const add_swc_dependencies_1 = require("@nx/js/src/utils/swc/add-swc-dependencies");
const chalk = require("chalk");
const show_possible_warnings_1 = require("./lib/show-possible-warnings");
const add_e2e_1 = require("./lib/add-e2e");
const eslint_file_1 = require("@nx/eslint/src/generators/utils/eslint-file");
const js_1 = require("@nx/js");
const log_show_project_command_1 = require("@nx/devkit/src/utils/log-show-project-command");
const setup_tailwind_1 = require("../setup-tailwind/setup-tailwind");
async function addLinting(host, options) {
    const tasks = [];
    if (options.linter === eslint_1.Linter.EsLint) {
        const lintTask = await (0, eslint_1.lintProjectGenerator)(host, {
            linter: options.linter,
            project: options.projectName,
            tsConfigPaths: [
                (0, devkit_1.joinPathFragments)(options.appProjectRoot, 'tsconfig.app.json'),
            ],
            unitTestRunner: options.unitTestRunner,
            skipFormat: true,
            rootProject: options.rootProject,
            skipPackageJson: options.skipPackageJson,
            addPlugin: options.addPlugin,
        });
        tasks.push(lintTask);
        if ((0, eslint_file_1.isEslintConfigSupported)(host)) {
            (0, eslint_file_1.addExtendsToLintConfig)(host, options.appProjectRoot, 'plugin:@nx/react');
        }
        if (!options.skipPackageJson) {
            const installTask = (0, devkit_1.addDependenciesToPackageJson)(host, lint_1.extraEslintDependencies.dependencies, lint_1.extraEslintDependencies.devDependencies);
            const addSwcTask = (0, add_swc_dependencies_1.addSwcDependencies)(host);
            tasks.push(installTask, addSwcTask);
        }
    }
    return (0, devkit_1.runTasksInSerial)(...tasks);
}
async function applicationGenerator(host, schema) {
    return await applicationGeneratorInternal(host, {
        addPlugin: false,
        projectNameAndRootFormat: 'derived',
        ...schema,
    });
}
exports.applicationGenerator = applicationGenerator;
async function applicationGeneratorInternal(host, schema) {
    const tasks = [];
    const options = await (0, normalize_options_1.normalizeOptions)(host, schema);
    (0, show_possible_warnings_1.showPossibleWarnings)(host, options);
    const jsInitTask = await (0, js_1.initGenerator)(host, {
        ...schema,
        tsConfigName: schema.rootProject ? 'tsconfig.json' : 'tsconfig.base.json',
        skipFormat: true,
    });
    tasks.push(jsInitTask);
    const initTask = await (0, init_1.default)(host, {
        ...options,
        skipFormat: true,
    });
    tasks.push(initTask);
    if (options.bundler === 'webpack') {
        const { webpackInitGenerator } = (0, devkit_1.ensurePackage)('@nx/webpack', versions_1.nxVersion);
        const webpackInitTask = await webpackInitGenerator(host, {
            skipPackageJson: options.skipPackageJson,
            skipFormat: true,
            addPlugin: options.addPlugin,
        });
        tasks.push(webpackInitTask);
        if (!options.skipPackageJson) {
            const { ensureDependencies } = await Promise.resolve().then(() => require('@nx/webpack/src/utils/ensure-dependencies'));
            tasks.push(ensureDependencies(host, { uiFramework: 'react' }));
        }
    }
    if (!options.rootProject) {
        (0, create_ts_config_1.extractTsConfigBase)(host);
    }
    (0, create_application_files_1.createApplicationFiles)(host, options);
    (0, add_project_1.addProject)(host, options);
    if (options.style === 'tailwind') {
        const twTask = await (0, setup_tailwind_1.setupTailwindGenerator)(host, {
            project: options.projectName,
        });
        tasks.push(twTask);
    }
    if (options.bundler === 'vite') {
        const { createOrEditViteConfig, viteConfigurationGenerator } = (0, devkit_1.ensurePackage)('@nx/vite', versions_1.nxVersion);
        // We recommend users use `import.meta.env.MODE` and other variables in their code to differentiate between production and development.
        // See: https://vitejs.dev/guide/env-and-mode.html
        if (host.exists((0, devkit_1.joinPathFragments)(options.appProjectRoot, 'src/environments'))) {
            host.delete((0, devkit_1.joinPathFragments)(options.appProjectRoot, 'src/environments'));
        }
        const viteTask = await viteConfigurationGenerator(host, {
            uiFramework: 'react',
            project: options.projectName,
            newProject: true,
            includeVitest: options.unitTestRunner === 'vitest',
            inSourceTests: options.inSourceTests,
            compiler: options.compiler,
            skipFormat: true,
            addPlugin: options.addPlugin,
        });
        tasks.push(viteTask);
        createOrEditViteConfig(host, {
            project: options.projectName,
            includeLib: false,
            includeVitest: options.unitTestRunner === 'vitest',
            inSourceTests: options.inSourceTests,
            rollupOptionsExternal: [
                "'react'",
                "'react-dom'",
                "'react/jsx-runtime'",
            ],
            imports: [
                options.compiler === 'swc'
                    ? `import react from '@vitejs/plugin-react-swc'`
                    : `import react from '@vitejs/plugin-react'`,
            ],
            plugins: ['react()'],
        }, false);
    }
    else if (options.bundler === 'rspack') {
        const { configurationGenerator } = (0, devkit_1.ensurePackage)('@nx/rspack', versions_1.nxRspackVersion);
        const rspackTask = await configurationGenerator(host, {
            project: options.projectName,
            main: (0, devkit_1.joinPathFragments)(options.appProjectRoot, (0, maybe_js_1.maybeJs)(options, `src/main.tsx`)),
            tsConfig: (0, devkit_1.joinPathFragments)(options.appProjectRoot, 'tsconfig.app.json'),
            target: 'web',
            newProject: true,
            framework: 'react',
        });
        tasks.push(rspackTask);
    }
    if (options.bundler !== 'vite' && options.unitTestRunner === 'vitest') {
        const { createOrEditViteConfig, vitestGenerator } = (0, devkit_1.ensurePackage)('@nx/vite', versions_1.nxVersion);
        const vitestTask = await vitestGenerator(host, {
            uiFramework: 'react',
            coverageProvider: 'v8',
            project: options.projectName,
            inSourceTests: options.inSourceTests,
            skipFormat: true,
            addPlugin: options.addPlugin,
        });
        tasks.push(vitestTask);
        createOrEditViteConfig(host, {
            project: options.projectName,
            includeLib: false,
            includeVitest: true,
            inSourceTests: options.inSourceTests,
            rollupOptionsExternal: [
                "'react'",
                "'react-dom'",
                "'react/jsx-runtime'",
            ],
            imports: [
                options.compiler === 'swc'
                    ? `import react from '@vitejs/plugin-react-swc'`
                    : `import react from '@vitejs/plugin-react'`,
            ],
            plugins: ['react()'],
        }, true);
    }
    if ((options.bundler === 'vite' || options.unitTestRunner === 'vitest') &&
        options.inSourceTests) {
        host.delete((0, devkit_1.joinPathFragments)(options.appProjectRoot, `src/app/${options.fileName}.spec.tsx`));
    }
    const lintTask = await addLinting(host, options);
    tasks.push(lintTask);
    const e2eTask = await (0, add_e2e_1.addE2e)(host, options);
    tasks.push(e2eTask);
    if (options.unitTestRunner === 'jest') {
        const jestTask = await (0, add_jest_1.addJest)(host, options);
        tasks.push(jestTask);
    }
    // Handle tsconfig.spec.json for jest or vitest
    (0, update_jest_config_1.updateSpecConfig)(host, options);
    const stylePreprocessorTask = (0, install_common_dependencies_1.installCommonDependencies)(host, options);
    tasks.push(stylePreprocessorTask);
    const styledTask = (0, add_styled_dependencies_1.addStyledModuleDependencies)(host, options);
    tasks.push(styledTask);
    const routingTask = (0, add_routing_1.addRouting)(host, options);
    tasks.push(routingTask);
    (0, set_defaults_1.setDefaults)(host, options);
    if (options.bundler === 'rspack' && options.style === 'styled-jsx') {
        devkit_1.logger.warn(`${chalk.bold('styled-jsx')} is not supported by ${chalk.bold('Rspack')}. We've added ${chalk.bold('babel-loader')} to your project, but using babel will slow down your build.`);
        tasks.push((0, devkit_1.addDependenciesToPackageJson)(host, {}, { 'babel-loader': versions_1.babelLoaderVersion }));
        host.write((0, devkit_1.joinPathFragments)(options.appProjectRoot, 'rspack.config.js'), (0, devkit_1.stripIndents) `
        const { composePlugins, withNx, withReact } = require('@nx/rspack');
        module.exports = composePlugins(withNx(), withReact(), (config) => {
          config.module.rules.push({
            test: /\\.[jt]sx$/i,
            use: [
              {
                loader: 'babel-loader',
                options: {
                  presets: ['@babel/preset-typescript'],
                  plugins: ['styled-jsx/babel'],
                },
              },
            ],
          });
          return config;
        });
        `);
    }
    if (!options.skipFormat) {
        await (0, devkit_1.formatFiles)(host);
    }
    tasks.push(() => {
        (0, log_show_project_command_1.logShowProjectCommand)(options.projectName);
    });
    return (0, devkit_1.runTasksInSerial)(...tasks);
}
exports.applicationGeneratorInternal = applicationGeneratorInternal;
exports.default = applicationGenerator;
