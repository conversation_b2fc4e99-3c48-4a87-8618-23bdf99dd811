{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "outDir": "<%= offsetFromRoot %>dist/out-tsc",
    "types": [
      "node",
      <% if (style === 'styled-jsx') { %>"@nx/react/typings/styled-jsx.d.ts",<% } %>
      "@nx/react/typings/cssmodule.d.ts",
      "@nx/react/typings/image.d.ts"
      ]
  },
  "exclude": ["src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.spec.tsx", "src/**/*.test.tsx", "src/**/*.spec.js", "src/**/*.test.js", "src/**/*.spec.jsx", "src/**/*.test.jsx"],
  "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"]
}
