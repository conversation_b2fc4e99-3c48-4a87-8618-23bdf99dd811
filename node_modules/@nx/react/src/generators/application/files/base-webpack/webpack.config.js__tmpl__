<% if (webpackPluginOptions) { %>
const { NxWebpackPlugin } = require('@nx/webpack');
const { NxReactWebpackPlugin } = require('@nx/react');
const { join } = require('path');

module.exports = {
  output: {
    path: join(__dirname, '<%= offsetFromRoot %><%= webpackPluginOptions.outputPath %>'),
  },
  devServer: {
    port: 4200
  },
  plugins: [
    new NxWebpackPlugin({
      tsConfig: '<%= webpackPluginOptions.tsConfig %>',
      compiler: '<%= webpackPluginOptions.compiler %>',
      main: '<%= webpackPluginOptions.main %>',
      index: '<%= webpackPluginOptions.index %>',
      baseHref: '<%= webpackPluginOptions.baseHref %>',
      assets: <%- JSON.stringify(webpackPluginOptions.assets) %>,
      styles: <%- JSON.stringify(webpackPluginOptions.styles) %>,
      outputHashing: process.env['NODE_ENV'] === 'production' ? 'all' : 'none',
      optimization: process.env['NODE_ENV'] === 'production',
    }),
    new NxReactWebpackPlugin({
      // Uncomment this line if you don't want to use SVGR
      // See: https://react-svgr.com/
      // svgr: false
    }),
  ],
};
<% } else { %>
const { composePlugins, withNx } = require('@nx/webpack');
const { withReact } = require('@nx/react');

// Nx plugins for webpack.
module.exports = composePlugins(
  withNx(),
  withReact({
    // Uncomment this line if you don't want to use SVGR
    // See: https://react-svgr.com/
    // svgr: false
  }),
  (config) => {
    // Update the webpack config as needed here.
    // e.g. `config.plugins.push(new MyPlugin())`
    return config;
  }
);
<% } %>
