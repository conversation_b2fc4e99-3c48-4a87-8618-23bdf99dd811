{"$schema": "https://json-schema.org/schema", "cli": "nx", "$id": "NxReactApp", "title": "Create a React Application", "description": "Create a React application for Nx.", "examples": [{"command": "nx g app myapp --directory=myorg", "description": "Generate `apps/myorg/myapp` and `apps/myorg/myapp-e2e`"}, {"command": "nx g app myapp --classComponent", "description": "Use class components instead of functional components"}, {"command": "nx g app myapp --routing", "description": "Set up React Router"}], "type": "object", "properties": {"name": {"description": "The name of the application.", "type": "string", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the application?", "pattern": "^[a-zA-Z][^:]*$"}, "directory": {"description": "The directory of the new application.", "type": "string", "alias": "dir", "x-priority": "important"}, "projectNameAndRootFormat": {"description": "Whether to generate the project name and root directory as provided (`as-provided`) or generate them composing their values and taking the configured layout into account (`derived`).", "type": "string", "enum": ["as-provided", "derived"]}, "style": {"description": "The file extension to be used for style files.", "type": "string", "default": "css", "alias": "s", "x-prompt": {"message": "Which stylesheet format would you like to use?", "type": "list", "items": [{"value": "css", "label": "CSS"}, {"value": "scss", "label": "SASS(.scss)       [ https://sass-lang.com                     ]"}, {"value": "less", "label": "LESS              [ https://lesscss.org                       ]"}, {"value": "tailwind", "label": "tailwind [ https://tailwindcss.com/            ]"}, {"value": "styled-components", "label": "styled-components [ https://styled-components.com            ]"}, {"value": "@emotion/styled", "label": "emotion           [ https://emotion.sh                       ]"}, {"value": "styled-jsx", "label": "styled-jsx        [ https://www.npmjs.com/package/styled-jsx ]"}, {"value": "none", "label": "None"}]}}, "linter": {"description": "The tool to use for running lint checks.", "type": "string", "enum": ["eslint", "none"], "default": "eslint"}, "routing": {"type": "boolean", "description": "Generate application with routes.", "x-prompt": "Would you like to add React Router to this application?", "default": false}, "skipFormat": {"description": "Skip formatting files.", "type": "boolean", "default": false, "x-priority": "internal"}, "skipNxJson": {"description": "Skip updating `nx.json` with default options based on values provided to this app.", "type": "boolean", "default": false, "x-priority": "internal"}, "unitTestRunner": {"type": "string", "enum": ["jest", "vitest", "none"], "description": "Test runner to use for unit tests.", "default": "jest"}, "inSourceTests": {"type": "boolean", "default": false, "description": "When using Vitest, separate spec files will not be generated and instead will be included within the source files. Read more on the Vitest docs site: https://vitest.dev/guide/in-source.html"}, "e2eTestRunner": {"type": "string", "enum": ["cypress", "playwright", "none"], "description": "Test runner to use for end to end (E2E) tests.", "x-prompt": "Which E2E test runner would you like to use?", "default": "cypress"}, "tags": {"type": "string", "description": "Add tags to the application (used for linting).", "alias": "t"}, "pascalCaseFiles": {"type": "boolean", "description": "Use pascal case component file name (e.g. `App.tsx`).", "alias": "P", "default": false}, "classComponent": {"type": "boolean", "description": "Use class components instead of functional component.", "alias": "C", "default": false}, "js": {"type": "boolean", "description": "Generate JavaScript files rather than TypeScript files.", "default": false}, "globalCss": {"type": "boolean", "description": "Default is `false`. When `true`, the component is generated with `*.css`/`*.scss` instead of `*.module.css`/`*.module.scss`.", "default": false}, "strict": {"type": "boolean", "description": "Creates an application with strict mode and strict type checking.", "default": true}, "setParserOptionsProject": {"type": "boolean", "description": "Whether or not to configure the ESLint `parserOptions.project` option. We do not do this by default for lint performance reasons.", "default": false}, "compiler": {"type": "string", "description": "The compiler to use.", "enum": ["babel", "swc"], "default": "babel"}, "skipPackageJson": {"description": "Do not add dependencies to `package.json`.", "type": "boolean", "default": false, "x-priority": "internal"}, "rootProject": {"description": "Create a application at the root of the workspace", "type": "boolean", "default": false, "hidden": true}, "bundler": {"description": "The bundler to use.", "type": "string", "enum": ["vite", "webpack", "rspack"], "x-prompt": "Which bundler do you want to use to build the application?", "default": "webpack", "x-priority": "important"}, "minimal": {"description": "Generate a React app with a minimal setup, no separate test files.", "type": "boolean", "default": false}}, "required": ["name"], "examplesFile": "../../../docs/application-examples.md"}