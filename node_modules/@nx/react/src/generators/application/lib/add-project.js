"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addProject = void 0;
const devkit_1 = require("@nx/devkit");
const has_webpack_plugin_1 = require("../../../utils/has-webpack-plugin");
const maybe_js_1 = require("../../../utils/maybe-js");
function addProject(host, options) {
    const project = {
        root: options.appProjectRoot,
        sourceRoot: `${options.appProjectRoot}/src`,
        projectType: 'application',
        targets: {},
        tags: options.parsedTags,
    };
    if (options.bundler === 'webpack') {
        if (!(0, has_webpack_plugin_1.hasWebpackPlugin)(host) || !options.addPlugin) {
            project.targets = {
                build: createBuildTarget(options),
                serve: createServeTarget(options),
            };
        }
    }
    (0, devkit_1.addProjectConfiguration)(host, options.projectName, {
        ...project,
    });
}
exports.addProject = addProject;
function createBuildTarget(options) {
    return {
        executor: '@nx/webpack:webpack',
        outputs: ['{options.outputPath}'],
        defaultConfiguration: 'production',
        options: {
            compiler: options.compiler ?? 'babel',
            outputPath: (0, devkit_1.joinPathFragments)('dist', options.appProjectRoot != '.'
                ? options.appProjectRoot
                : options.projectName),
            index: (0, devkit_1.joinPathFragments)(options.appProjectRoot, 'src/index.html'),
            baseHref: '/',
            main: (0, devkit_1.joinPathFragments)(options.appProjectRoot, (0, maybe_js_1.maybeJs)(options, `src/main.tsx`)),
            tsConfig: (0, devkit_1.joinPathFragments)(options.appProjectRoot, 'tsconfig.app.json'),
            assets: [
                (0, devkit_1.joinPathFragments)(options.appProjectRoot, 'src/favicon.ico'),
                (0, devkit_1.joinPathFragments)(options.appProjectRoot, 'src/assets'),
            ],
            styles: options.styledModule || !options.hasStyles
                ? []
                : [
                    (0, devkit_1.joinPathFragments)(options.appProjectRoot, `src/styles.${options.style}`),
                ],
            scripts: [],
            webpackConfig: (0, devkit_1.joinPathFragments)(options.appProjectRoot, 'webpack.config.js'),
        },
        configurations: {
            development: {
                extractLicenses: false,
                optimization: false,
                sourceMap: true,
                vendorChunk: true,
            },
            production: {
                fileReplacements: [
                    {
                        replace: (0, devkit_1.joinPathFragments)(options.appProjectRoot, (0, maybe_js_1.maybeJs)(options, `src/environments/environment.ts`)),
                        with: (0, devkit_1.joinPathFragments)(options.appProjectRoot, (0, maybe_js_1.maybeJs)(options, `src/environments/environment.prod.ts`)),
                    },
                ],
                optimization: true,
                outputHashing: 'all',
                sourceMap: false,
                namedChunks: false,
                extractLicenses: true,
                vendorChunk: false,
            },
        },
    };
}
function createServeTarget(options) {
    return {
        executor: '@nx/webpack:dev-server',
        defaultConfiguration: 'development',
        options: {
            buildTarget: `${options.projectName}:build`,
            hmr: true,
        },
        configurations: {
            development: {
                buildTarget: `${options.projectName}:build:development`,
            },
            production: {
                buildTarget: `${options.projectName}:build:production`,
                hmr: false,
            },
        },
    };
}
