"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApplicationFiles = void 0;
const devkit_1 = require("@nx/devkit");
const js_1 = require("@nx/js");
const path_1 = require("path");
const create_ts_config_1 = require("../../../utils/create-ts-config");
const get_in_source_vitest_tests_template_1 = require("../../../utils/get-in-source-vitest-tests-template");
const maybe_js_1 = require("../../../utils/maybe-js");
const has_webpack_plugin_1 = require("../../../utils/has-webpack-plugin");
const get_app_tests_1 = require("./get-app-tests");
function createApplicationFiles(host, options) {
    let styleSolutionSpecificAppFiles;
    if (options.styledModule && options.style !== 'styled-jsx') {
        styleSolutionSpecificAppFiles = '../files/style-styled-module';
    }
    else if (options.style === 'styled-jsx') {
        styleSolutionSpecificAppFiles = '../files/style-styled-jsx';
    }
    else if (options.style === 'tailwind') {
        styleSolutionSpecificAppFiles = '../files/style-tailwind';
    }
    else if (options.style === 'none') {
        styleSolutionSpecificAppFiles = '../files/style-none';
    }
    else if (options.globalCss) {
        styleSolutionSpecificAppFiles = '../files/style-global-css';
    }
    else {
        styleSolutionSpecificAppFiles = '../files/style-css-module';
    }
    const relativePathToRootTsConfig = (0, js_1.getRelativePathToRootTsConfig)(host, options.appProjectRoot);
    const appTests = (0, get_app_tests_1.getAppTests)(options);
    const templateVariables = {
        ...(0, devkit_1.names)(options.name),
        ...options,
        tmpl: '',
        offsetFromRoot: (0, devkit_1.offsetFromRoot)(options.appProjectRoot),
        appTests,
        inSourceVitestTests: (0, get_in_source_vitest_tests_template_1.getInSourceVitestTestsTemplate)(appTests),
    };
    if (options.bundler === 'vite') {
        (0, devkit_1.generateFiles)(host, (0, path_1.join)(__dirname, '../files/base-vite'), options.appProjectRoot, templateVariables);
    }
    else if (options.bundler === 'webpack') {
        (0, devkit_1.generateFiles)(host, (0, path_1.join)(__dirname, '../files/base-webpack'), options.appProjectRoot, {
            ...templateVariables,
            webpackPluginOptions: (0, has_webpack_plugin_1.hasWebpackPlugin)(host)
                ? createNxWebpackPluginOptions(options)
                : null,
        });
        if (options.compiler === 'babel') {
            (0, devkit_1.writeJson)(host, `${options.appProjectRoot}/.babelrc`, {
                presets: [
                    [
                        '@nx/react/babel',
                        {
                            runtime: 'automatic',
                            importSource: options.style === '@emotion/styled'
                                ? '@emotion/react'
                                : undefined,
                        },
                    ],
                ],
                plugins: [
                    options.style === 'styled-components'
                        ? ['styled-components', { pure: true, ssr: true }]
                        : undefined,
                    options.style === 'styled-jsx' ? 'styled-jsx/babel' : undefined,
                    options.style === '@emotion/styled'
                        ? '@emotion/babel-plugin'
                        : undefined,
                ].filter(Boolean),
            });
        }
        else if (options.compiler === 'swc') {
            const swcrc = {
                jsc: {
                    target: 'es2016',
                },
            };
            if (options.style === 'styled-components') {
                swcrc.jsc.experimental = {
                    plugins: [
                        [
                            '@swc/plugin-styled-components',
                            {
                                displayName: true,
                                ssr: true,
                            },
                        ],
                    ],
                };
            }
            else if (options.style === '@emotion/styled') {
                swcrc.jsc.experimental = {
                    plugins: [['@swc/plugin-emotion', {}]],
                };
            }
            else if (options.style === 'styled-jsx') {
                swcrc.jsc.experimental = {
                    plugins: [['@swc/plugin-styled-jsx', {}]],
                };
            }
            (0, devkit_1.writeJson)(host, `${options.appProjectRoot}/.swcrc`, swcrc);
        }
    }
    else if (options.bundler === 'rspack') {
        (0, devkit_1.generateFiles)(host, (0, path_1.join)(__dirname, '../files/base-rspack'), options.appProjectRoot, templateVariables);
    }
    if (options.unitTestRunner === 'none' ||
        (options.unitTestRunner === 'vitest' && options.inSourceTests == true)) {
        host.delete(`${options.appProjectRoot}/src/app/${options.fileName}.spec.tsx`);
    }
    if (!options.minimal) {
        (0, devkit_1.generateFiles)(host, (0, path_1.join)(__dirname, '../files/nx-welcome'), options.appProjectRoot, templateVariables);
    }
    (0, devkit_1.generateFiles)(host, (0, path_1.join)(__dirname, styleSolutionSpecificAppFiles), options.appProjectRoot, templateVariables);
    if (options.js) {
        (0, devkit_1.toJS)(host);
    }
    (0, create_ts_config_1.createTsConfig)(host, options.appProjectRoot, 'app', options, relativePathToRootTsConfig);
}
exports.createApplicationFiles = createApplicationFiles;
function createNxWebpackPluginOptions(options) {
    return {
        target: 'web',
        compiler: options.compiler ?? 'babel',
        outputPath: (0, devkit_1.joinPathFragments)('dist', options.appProjectRoot != '.'
            ? options.appProjectRoot
            : options.projectName),
        index: './src/index.html',
        baseHref: '/',
        main: (0, maybe_js_1.maybeJs)(options, `./src/main.tsx`),
        tsConfig: './tsconfig.app.json',
        assets: ['./src/favicon.ico', './src/assets'],
        styles: options.styledModule || !options.hasStyles
            ? []
            : [
                `./src/styles.${options.style !== 'tailwind' ? options.style : 'css'}`,
            ],
    };
}
