"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateSpecConfig = void 0;
const maybe_js_1 = require("../../../utils/maybe-js");
const jest_utils_1 = require("../../../utils/jest-utils");
const devkit_1 = require("@nx/devkit");
function updateSpecConfig(host, options) {
    if (options.unitTestRunner === 'none') {
        return;
    }
    (0, devkit_1.updateJson)(host, `${options.appProjectRoot}/tsconfig.spec.json`, (json) => {
        const compilerOptions = json.compilerOptions ?? {};
        const types = compilerOptions.types ?? [];
        if (options.style === 'styled-jsx') {
            types.push('@nx/react/typings/styled-jsx.d.ts');
        }
        types.push('@nx/react/typings/cssmodule.d.ts', '@nx/react/typings/image.d.ts');
        compilerOptions.types = types;
        json.compilerOptions = compilerOptions;
        return json;
    });
    if (options.unitTestRunner !== 'jest') {
        return;
    }
    const configPath = (0, maybe_js_1.maybeJs)(options, `${options.appProjectRoot}/jest.config.ts`);
    const originalContent = host.read(configPath, 'utf-8');
    const content = (0, jest_utils_1.updateJestConfigContent)(originalContent);
    host.write(configPath, content);
}
exports.updateSpecConfig = updateSpecConfig;
