"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addJest = void 0;
const devkit_1 = require("@nx/devkit");
const versions_1 = require("../../../utils/versions");
async function addJest(host, options) {
    if (options.unitTestRunner === 'none') {
        return () => { };
    }
    const { configurationGenerator } = (0, devkit_1.ensurePackage)('@nx/jest', versions_1.nxVersion);
    return await configurationGenerator(host, {
        ...options,
        project: options.projectName,
        supportTsx: true,
        skipSerializers: true,
        setupFile: 'none',
        compiler: options.compiler,
        skipFormat: true,
    });
}
exports.addJest = addJest;
