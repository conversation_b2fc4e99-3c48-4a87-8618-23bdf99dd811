"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setDefaults = void 0;
const devkit_1 = require("@nx/devkit");
function setDefaults(host, options) {
    if (options.skipNxJson) {
        return;
    }
    const nxJson = (0, devkit_1.readNxJson)(host);
    if (options.rootProject) {
        nxJson.defaultProject = options.projectName;
    }
    nxJson.generators = nxJson.generators || {};
    nxJson.generators['@nx/react'] = nxJson.generators['@nx/react'] || {};
    const prev = { ...nxJson.generators['@nx/react'] };
    const appDefaults = {
        babel: true,
        style: options.style,
        linter: options.linter,
        bundler: options.bundler,
        ...prev.application,
    };
    const componentDefaults = {
        style: options.style,
        ...prev.component,
    };
    const libDefaults = {
        style: options.style,
        linter: options.linter,
        ...prev.library,
    };
    nxJson.generators = {
        ...nxJson.generators,
        '@nx/react': {
            ...prev,
            application: appDefaults,
            component: componentDefaults,
            library: libDefaults,
        },
    };
    (0, devkit_1.updateNxJson)(host, nxJson);
}
exports.setDefaults = setDefaults;
