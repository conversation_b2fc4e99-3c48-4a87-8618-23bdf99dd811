"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findFreePort = void 0;
const devkit_1 = require("@nx/devkit");
function findFreePort(host) {
    const projects = (0, devkit_1.getProjects)(host);
    let port = -Infinity;
    for (const [, p] of projects.entries()) {
        const curr = p.targets?.serve?.options?.port;
        if (typeof curr === 'number') {
            port = Math.max(port, curr);
        }
    }
    return port > 0 ? port + 1 : 4200;
}
exports.findFreePort = findFreePort;
