"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.normalizeOptions = exports.normalizeProjectName = exports.normalizeDirectory = void 0;
const devkit_1 = require("@nx/devkit");
const project_name_and_root_utils_1 = require("@nx/devkit/src/generators/project-name-and-root-utils");
const assertion_1 = require("../../../utils/assertion");
const find_free_port_1 = require("./find-free-port");
function normalizeDirectory(options) {
    options.directory = options.directory?.replace(/\\{1,2}/g, '/');
    const { projectDirectory } = (0, devkit_1.extractLayoutDirectory)(options.directory);
    return projectDirectory
        ? `${(0, devkit_1.names)(projectDirectory).fileName}/${(0, devkit_1.names)(options.name).fileName}`
        : (0, devkit_1.names)(options.name).fileName;
}
exports.normalizeDirectory = normalizeDirectory;
function normalizeProjectName(options) {
    return normalizeDirectory(options).replace(new RegExp('/', 'g'), '-');
}
exports.normalizeProjectName = normalizeProjectName;
async function normalizeOptions(host, options, callingGenerator = '@nx/react:application') {
    const { projectName: appProjectName, projectRoot: appProjectRoot, projectNameAndRootFormat, } = await (0, project_name_and_root_utils_1.determineProjectNameAndRootOptions)(host, {
        name: options.name,
        projectType: 'application',
        directory: options.directory,
        projectNameAndRootFormat: options.projectNameAndRootFormat,
        rootProject: options.rootProject,
        callingGenerator,
    });
    const nxJson = (0, devkit_1.readNxJson)(host);
    const addPlugin = process.env.NX_ADD_PLUGINS !== 'false' &&
        nxJson.useInferencePlugins !== false;
    options.addPlugin ??= addPlugin;
    options.rootProject = appProjectRoot === '.';
    options.projectNameAndRootFormat = projectNameAndRootFormat;
    let e2eWebServerTarget = 'serve';
    if (options.addPlugin) {
        if (nxJson.plugins) {
            for (const plugin of nxJson.plugins) {
                if (options.bundler === 'vite' &&
                    typeof plugin === 'object' &&
                    plugin.plugin === '@nx/vite/plugin' &&
                    plugin.options.serveTargetName) {
                    e2eWebServerTarget = plugin.options
                        .serveTargetName;
                }
                else if (options.bundler === 'webpack' &&
                    typeof plugin === 'object' &&
                    plugin.plugin === '@nx/webpack/plugin' &&
                    plugin.options.serveTargetName) {
                    e2eWebServerTarget = plugin.options
                        .serveTargetName;
                }
            }
        }
    }
    let e2ePort = options.devServerPort ?? 4200;
    if (nxJson.targetDefaults?.[e2eWebServerTarget] &&
        nxJson.targetDefaults?.[e2eWebServerTarget].options?.port) {
        e2ePort = nxJson.targetDefaults?.[e2eWebServerTarget].options?.port;
    }
    const e2eProjectName = options.rootProject ? 'e2e' : `${appProjectName}-e2e`;
    const e2eProjectRoot = options.rootProject ? 'e2e' : `${appProjectRoot}-e2e`;
    const e2eWebServerAddress = `http://localhost:${e2ePort}`;
    const parsedTags = options.tags
        ? options.tags.split(',').map((s) => s.trim())
        : [];
    const fileName = options.pascalCaseFiles ? 'App' : 'app';
    const styledModule = /^(css|scss|less|tailwind|none)$/.test(options.style)
        ? null
        : options.style;
    (0, assertion_1.assertValidStyle)(options.style);
    if (options.bundler === 'vite' && options.unitTestRunner !== 'none') {
        options.unitTestRunner = 'vitest';
    }
    const normalized = {
        ...options,
        name: (0, devkit_1.names)(options.name).fileName,
        projectName: appProjectName,
        appProjectRoot,
        e2eProjectName,
        e2eProjectRoot,
        e2eWebServerAddress,
        e2eWebServerTarget,
        e2ePort,
        parsedTags,
        fileName,
        styledModule,
        hasStyles: options.style !== 'none',
    };
    normalized.routing = normalized.routing ?? false;
    normalized.strict = normalized.strict ?? true;
    normalized.classComponent = normalized.classComponent ?? false;
    normalized.compiler = normalized.compiler ?? 'babel';
    normalized.bundler = normalized.bundler ?? 'webpack';
    normalized.unitTestRunner = normalized.unitTestRunner ?? 'jest';
    normalized.e2eTestRunner = normalized.e2eTestRunner ?? 'cypress';
    normalized.inSourceTests = normalized.minimal || normalized.inSourceTests;
    normalized.devServerPort ??= (0, find_free_port_1.findFreePort)(host);
    normalized.minimal = normalized.minimal ?? false;
    return normalized;
}
exports.normalizeOptions = normalizeOptions;
