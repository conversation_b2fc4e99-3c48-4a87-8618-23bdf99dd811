{"name": "@nx/react", "version": "18.3.3", "private": false, "description": "The React plugin for Nx contains executors and generators for managing React applications and libraries within an Nx workspace. It provides:\n\n\n- Integration with libraries such as Jest, Cypress, and Storybook.\n\n- Generators for applications, libraries, components, hooks, and more.\n\n- Library build support for publishing packages to npm or other registries.\n\n- Utilities for automatic workspace refactoring.", "repository": {"type": "git", "url": "https://github.com/nrwl/nx.git", "directory": "packages/react"}, "keywords": ["Monore<PERSON>", "React", "Web", "Jest", "Cypress", "CLI"], "main": "./index.js", "typings": "./index.d.ts", "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/nrwl/nx/issues"}, "homepage": "https://nx.dev", "executors": "./executors.json", "generators": "./generators.json", "ng-update": {"requirements": {}, "migrations": "./migrations.json"}, "dependencies": {"@phenomnomnominal/tsquery": "~5.0.1", "@svgr/webpack": "^8.0.1", "chalk": "^4.1.0", "file-loader": "^6.2.0", "minimatch": "9.0.3", "tslib": "^2.3.0", "@nx/devkit": "18.3.3", "@nx/js": "18.3.3", "@nx/eslint": "18.3.3", "@nx/web": "18.3.3", "@nrwl/react": "18.3.3"}, "publishConfig": {"access": "public"}, "type": "commonjs"}