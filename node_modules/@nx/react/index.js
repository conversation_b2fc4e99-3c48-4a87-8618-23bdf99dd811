"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NxReactWebpackPlugin = exports.setupTailwindGenerator = exports.componentTestGenerator = exports.cypressComponentConfigGenerator = exports.remoteGenerator = exports.hostGenerator = exports.storybookConfigurationGenerator = exports.storiesGenerator = exports.reduxGenerator = exports.reactInitGenerator = exports.libraryGenerator = exports.componentStoryGenerator = exports.componentCypressGenerator = exports.hookGenerator = exports.componentGenerator = exports.applicationGenerator = exports.reactVersion = exports.reactDomVersion = exports.assertValidStyle = exports.cssInJsDependenciesBabel = exports.extendReactEslintJson = exports.extraEslintDependencies = void 0;
const tslib_1 = require("tslib");
var lint_1 = require("./src/utils/lint");
Object.defineProperty(exports, "extraEslintDependencies", { enumerable: true, get: function () { return lint_1.extraEslintDependencies; } });
Object.defineProperty(exports, "extendReactEslintJson", { enumerable: true, get: function () { return lint_1.extendReactEslintJson; } });
var styled_1 = require("./src/utils/styled");
Object.defineProperty(exports, "cssInJsDependenciesBabel", { enumerable: true, get: function () { return styled_1.cssInJsDependenciesBabel; } });
var assertion_1 = require("./src/utils/assertion");
Object.defineProperty(exports, "assertValidStyle", { enumerable: true, get: function () { return assertion_1.assertValidStyle; } });
var versions_1 = require("./src/utils/versions");
Object.defineProperty(exports, "reactDomVersion", { enumerable: true, get: function () { return versions_1.reactDomVersion; } });
Object.defineProperty(exports, "reactVersion", { enumerable: true, get: function () { return versions_1.reactVersion; } });
var application_1 = require("./src/generators/application/application");
Object.defineProperty(exports, "applicationGenerator", { enumerable: true, get: function () { return application_1.applicationGenerator; } });
var component_1 = require("./src/generators/component/component");
Object.defineProperty(exports, "componentGenerator", { enumerable: true, get: function () { return component_1.componentGenerator; } });
var hook_1 = require("./src/generators/hook/hook");
Object.defineProperty(exports, "hookGenerator", { enumerable: true, get: function () { return hook_1.hookGenerator; } });
var component_cypress_spec_1 = require("./src/generators/component-cypress-spec/component-cypress-spec");
Object.defineProperty(exports, "componentCypressGenerator", { enumerable: true, get: function () { return component_cypress_spec_1.componentCypressGenerator; } });
var component_story_1 = require("./src/generators/component-story/component-story");
Object.defineProperty(exports, "componentStoryGenerator", { enumerable: true, get: function () { return component_story_1.componentStoryGenerator; } });
var library_1 = require("./src/generators/library/library");
Object.defineProperty(exports, "libraryGenerator", { enumerable: true, get: function () { return library_1.libraryGenerator; } });
var init_1 = require("./src/generators/init/init");
Object.defineProperty(exports, "reactInitGenerator", { enumerable: true, get: function () { return init_1.reactInitGenerator; } });
var redux_1 = require("./src/generators/redux/redux");
Object.defineProperty(exports, "reduxGenerator", { enumerable: true, get: function () { return redux_1.reduxGenerator; } });
var stories_1 = require("./src/generators/stories/stories");
Object.defineProperty(exports, "storiesGenerator", { enumerable: true, get: function () { return stories_1.storiesGenerator; } });
var configuration_1 = require("./src/generators/storybook-configuration/configuration");
Object.defineProperty(exports, "storybookConfigurationGenerator", { enumerable: true, get: function () { return configuration_1.storybookConfigurationGenerator; } });
var host_1 = require("./src/generators/host/host");
Object.defineProperty(exports, "hostGenerator", { enumerable: true, get: function () { return host_1.hostGenerator; } });
var remote_1 = require("./src/generators/remote/remote");
Object.defineProperty(exports, "remoteGenerator", { enumerable: true, get: function () { return remote_1.remoteGenerator; } });
var cypress_component_configuration_1 = require("./src/generators/cypress-component-configuration/cypress-component-configuration");
Object.defineProperty(exports, "cypressComponentConfigGenerator", { enumerable: true, get: function () { return cypress_component_configuration_1.cypressComponentConfigGenerator; } });
var component_test_1 = require("./src/generators/component-test/component-test");
Object.defineProperty(exports, "componentTestGenerator", { enumerable: true, get: function () { return component_test_1.componentTestGenerator; } });
var setup_tailwind_1 = require("./src/generators/setup-tailwind/setup-tailwind");
Object.defineProperty(exports, "setupTailwindGenerator", { enumerable: true, get: function () { return setup_tailwind_1.setupTailwindGenerator; } });
tslib_1.__exportStar(require("./plugins/with-react"), exports);
var nx_react_webpack_plugin_1 = require("./plugins/nx-react-webpack-plugin/nx-react-webpack-plugin");
Object.defineProperty(exports, "NxReactWebpackPlugin", { enumerable: true, get: function () { return nx_react_webpack_plugin_1.NxReactWebpackPlugin; } });
