interface NxReactBabelOptions {
    development?: boolean;
    runtime?: string;
    importSource?: string;
    useBuiltIns?: boolean | string;
    decorators?: {
        decoratorsBeforeExport?: boolean;
        legacy?: boolean;
    };
    loose?: boolean;
    /** @deprecated Use `loose` option instead of `classProperties.loose`
     */
    classProperties?: {
        loose?: boolean;
    };
}
declare function getReactPresetOptions({ presetOptions, env, }: {
    env: string;
    presetOptions: NxReactBabelOptions;
}): Record<string, string | boolean>;
