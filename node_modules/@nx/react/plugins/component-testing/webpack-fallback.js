"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildBaseWebpackConfig = void 0;
const tsconfig_paths_webpack_plugin_1 = require("tsconfig-paths-webpack-plugin");
const webpack_1 = require("@nx/webpack");
function buildBaseWebpackConfig({ tsConfigPath = 'tsconfig.cy.json', compiler = 'babel', }) {
    const extensions = ['.ts', '.tsx', '.mjs', '.js', '.jsx'];
    const config = {
        target: 'web',
        resolve: {
            extensions,
            plugins: [
                new tsconfig_paths_webpack_plugin_1.TsconfigPathsPlugin({
                    configFile: tsConfigPath,
                    extensions,
                }),
            ],
        },
        mode: 'development',
        devtool: false,
        output: {
            publicPath: '/',
            chunkFilename: '[name].bundle.js',
        },
        module: {
            rules: [
                {
                    test: /\.(bmp|png|jpe?g|gif|webp|avif)$/,
                    type: 'asset',
                    parser: {
                        dataUrlCondition: {
                            maxSize: 10_000, // 10 kB
                        },
                    },
                },
                CSS_MODULES_LOADER,
            ],
        },
    };
    if (compiler === 'swc') {
        config.module.rules.push({
            test: /\.([jt])sx?$/,
            loader: require.resolve('swc-loader'),
            exclude: /node_modules/,
            options: {
                jsc: {
                    parser: {
                        syntax: 'typescript',
                        decorators: true,
                        tsx: true,
                    },
                    transform: {
                        react: {
                            runtime: 'automatic',
                        },
                    },
                    loose: true,
                },
            },
        });
    }
    if (compiler === 'babel') {
        config.module.rules.push({
            test: /\.(js|jsx|mjs|ts|tsx)$/,
            loader: require.resolve('babel-loader'),
            options: {
                presets: [`@nx/react/babel`],
                rootMode: 'upward',
                babelrc: true,
            },
        });
    }
    return config;
}
exports.buildBaseWebpackConfig = buildBaseWebpackConfig;
const loaderModulesOptions = {
    modules: {
        mode: 'local',
        getLocalIdent: webpack_1.getCSSModuleLocalIdent,
    },
    importLoaders: 1,
};
const commonLoaders = [
    {
        loader: require.resolve('style-loader'),
    },
    {
        loader: require.resolve('css-loader'),
        options: loaderModulesOptions,
    },
];
const CSS_MODULES_LOADER = {
    test: /\.css$|\.scss$|\.sass$|\.less$/,
    oneOf: [
        {
            test: /\.module\.css$/,
            use: commonLoaders,
        },
        {
            test: /\.module\.(scss|sass)$/,
            use: [
                ...commonLoaders,
                {
                    loader: require.resolve('sass-loader'),
                    options: {
                        implementation: require('sass'),
                        sassOptions: {
                            fiber: false,
                            precision: 8,
                        },
                    },
                },
            ],
        },
        {
            test: /\.module\.less$/,
            use: [
                ...commonLoaders,
                {
                    loader: require.resolve('less-loader'),
                },
            ],
        },
    ],
};
