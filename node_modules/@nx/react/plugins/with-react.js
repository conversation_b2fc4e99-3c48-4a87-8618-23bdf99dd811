"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.withReact = void 0;
const apply_react_config_1 = require("./nx-react-webpack-plugin/lib/apply-react-config");
const processed = new Set();
/**
 * @param {WithReactOptions} pluginOptions
 * @returns {NxWebpackPlugin}
 */
function withReact(pluginOptions = {}) {
    return function configure(config, context) {
        const { withWeb } = require('@nx/webpack');
        if (processed.has(config))
            return config;
        // Apply web config for CSS, JSX, index.html handling, etc.
        config = withWeb(pluginOptions)(config, context);
        (0, apply_react_config_1.applyReactConfig)(pluginOptions, config);
        processed.add(config);
        return config;
    };
}
exports.withReact = withReact;
