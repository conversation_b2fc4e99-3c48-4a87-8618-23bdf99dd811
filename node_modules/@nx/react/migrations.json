{"generators": {"update-rollup-executor": {"cli": "nx", "version": "15.3.0-beta.0", "description": "Update projects using @nrwl/web:rollup to @nrwl/rollup:rollup for build.", "factory": "./src/migrations/update-15-3-0/update-rollup-executor"}, "install-webpack-rollup-dependencies": {"cli": "nx", "version": "15.3.0-beta.0", "description": "Install new dependencies for React projects using Webpack or Rollup.", "factory": "./src/migrations/update-15-3-0/install-webpack-rollup-dependencies"}, "react-webpack-config-setup": {"cli": "nx", "version": "15.6.3-beta.0", "description": "Creates or updates webpack.config.js file with the new options for webpack.", "factory": "./src/migrations/update-15-6-3/webpack-config-setup"}, "update-16-0-0-add-nx-packages": {"cli": "nx", "version": "16.0.0-beta.1", "description": "Replace @nrwl/react with @nx/react", "implementation": "./src/migrations/update-16-0-0-add-nx-packages/update-16-0-0-add-nx-packages"}, "update-16-2-0-remove-package": {"cli": "nx", "version": "16.2.0-beta.0", "description": "Remove react-test-renderer from package.json", "implementation": "./src/migrations/update-16-2-0-remove-package/update-16-2-0-remove-package"}, "remove-types-react-router-dom": {"cli": "nx", "version": "16.3.0-beta.2", "description": "Remove @types/react-router-dom from package.json", "implementation": "./src/migrations/update-16-3-0/remove-types-react-router-dom-package"}, "add-babel-core": {"cli": "nx", "version": "16.7.0-beta.2", "description": "Add @babel/core to package.json if @babel/preset-react is present", "implementation": "./src/migrations/update-16-7-0/add-babel-core"}, "update-16-7-0-add-typings": {"cli": "nx", "version": "16.7.0-beta.2", "description": "Add @nx/react types to tsconfig types array", "implementation": "./src/migrations/update-16-7-0-add-typings/update-16-7-0-add-typings"}, "add-module-federation-env-var-to-target-defaults": {"cli": "nx", "version": "18.0.0-beta.0", "description": "Add NX_MF_DEV_SERVER_STATIC_REMOTES to inputs for task hashing when '@nx/webpack:webpack' is used for Module Federation.", "factory": "./src/migrations/update-18-0-0/add-mf-env-var-to-target-defaults"}, "fix-target-defaults-for-webpack": {"cli": "nx", "version": "18.1.1-beta.0", "description": "Ensure targetDefaults inputs for task hashing when '@nx/webpack:webpack' is used are correct for Module Federation.", "factory": "./src/migrations/update-18-1-1/fix-target-defaults-inputs"}}, "packageJsonUpdates": {"15.2.2-beta.0": {"version": "15.2.2-beta.0", "packages": {"@types/react": {"version": "18.0.25", "alwaysAddToPackageJson": false}, "@types/react-dom": {"version": "18.0.9", "alwaysAddToPackageJson": false}, "@types/node": {"version": "18.11.9", "alwaysAddToPackageJson": false}, "styled-components": {"version": "5.3.6", "alwaysAddToPackageJson": false}, "@emotion/styled": {"version": "11.10.5", "alwaysAddToPackageJson": false}, "@emotion/react": {"version": "11.10.5", "alwaysAddToPackageJson": false}, "@emotion/babel-plugin": {"version": "11.10.5", "alwaysAddToPackageJson": false}, "styled-jsx": {"version": "5.1.0", "alwaysAddToPackageJson": false}, "react-router-dom": {"version": "6.4.3", "alwaysAddToPackageJson": false}, "@reduxjs/toolkit": {"version": "1.9.0", "alwaysAddToPackageJson": false}, "react-redux": {"version": "8.0.5", "alwaysAddToPackageJson": false}, "eslint-plugin-react": {"version": "7.31.11", "alwaysAddToPackageJson": false}, "postcss": {"version": "8.4.19", "alwaysAddToPackageJson": false}, "tailwindcss": {"version": "3.2.4", "alwaysAddToPackageJson": false}, "autoprefixer": {"version": "10.4.13", "alwaysAddToPackageJson": false}, "@types/express": {"version": "4.17.14", "alwaysAddToPackageJson": false}}}, "15.8.0": {"version": "15.8.0-beta.0", "packages": {"@types/react": {"version": "18.0.28", "alwaysAddToPackageJson": false}, "@types/react-dom": {"version": "18.0.11", "alwaysAddToPackageJson": false}, "@types/node": {"version": "18.14.2", "alwaysAddToPackageJson": false}, "@emotion/styled": {"version": "11.10.6", "alwaysAddToPackageJson": false}, "@emotion/react": {"version": "11.10.6", "alwaysAddToPackageJson": false}, "@emotion/babel-plugin": {"version": "11.10.6", "alwaysAddToPackageJson": false}, "styled-jsx": {"version": "5.1.2", "alwaysAddToPackageJson": false}, "react-router-dom": {"version": "6.8.1", "alwaysAddToPackageJson": false}, "@testing-library/react": {"version": "14.0.0", "alwaysAddToPackageJson": false}, "@reduxjs/toolkit": {"version": "1.9.3", "alwaysAddToPackageJson": false}, "eslint-plugin-import": {"version": "2.27.5", "alwaysAddToPackageJson": false}, "eslint-plugin-jsx-a11y": {"version": "6.7.1", "alwaysAddToPackageJson": false}, "eslint-plugin-react": {"version": "7.32.2", "alwaysAddToPackageJson": false}, "postcss": {"version": "8.4.21", "alwaysAddToPackageJson": false}, "tailwindcss": {"version": "3.2.7", "alwaysAddToPackageJson": false}, "@types/express": {"version": "4.17.17", "alwaysAddToPackageJson": false}, "less": {"version": "4.1.3", "alwaysAddToPackageJson": false}}}, "16.3.0": {"version": "16.3.0-beta.2", "packages": {"react-router-dom": {"version": "6.11.2", "alwaysAddToPackageJson": false}, "@svgr/rollup": {"version": "^8.0.1", "alwaysAddToPackageJson": false}}}, "16.4.0-beta.4": {"version": "16.4.0-beta.4", "packages": {"stylus": {"version": "^0.59.0", "alwaysAddToPackageJson": false}}}, "16.4.0-beta.7": {"version": "16.4.0-beta.7", "packages": {"@types/react": {"version": "18.2.12", "alwaysAddToPackageJson": false}, "@types/react-dom": {"version": "18.2.5", "alwaysAddToPackageJson": false}, "@types/react-is": {"version": "18.2.0", "alwaysAddToPackageJson": false}}}, "16.4.0-beta.8": {"version": "16.4.0-beta.8", "packages": {"@emotion/styled": {"version": "11.11.0", "alwaysAddToPackageJson": false}, "@emotion/react": {"version": "11.11.1", "alwaysAddToPackageJson": false}, "@emotion/babel-plugin": {"version": "11.11.0", "alwaysAddToPackageJson": false}}}, "16.4.0-beta.11": {"version": "16.4.0-beta.11", "packages": {"@types/react": {"version": "18.2.13", "alwaysAddToPackageJson": false}, "@types/react-dom": {"version": "18.2.6", "alwaysAddToPackageJson": false}, "@types/react-is": {"version": "18.2.1", "alwaysAddToPackageJson": false}}}, "16.5.0-beta.4": {"version": "16.5.0-beta.4", "packages": {"@types/react": {"version": "18.2.14", "alwaysAddToPackageJson": false}}}, "17.0.0-beta.0": {"version": "17.0.0-beta.0", "packages": {"@types/react": {"version": "18.2.24", "alwaysAddToPackageJson": false}, "@types/react-dom": {"version": "18.2.9", "alwaysAddToPackageJson": false}, "@types/react-is": {"version": "18.2.2", "alwaysAddToPackageJson": false}}}, "17.3.0": {"version": "17.3.0-beta.3", "packages": {"@types/node": {"version": "18.16.9", "alwaysAddToPackageJson": false}}}}}