{"name": "@algolia/autocomplete-preset-algolia", "description": "Presets for building autocomplete experiences with Algolia.", "version": "1.9.3", "license": "MIT", "homepage": "https://github.com/algolia/autocomplete", "repository": "algolia/autocomplete", "author": {"name": "Algolia, Inc.", "url": "https://www.algolia.com"}, "sideEffects": false, "files": ["dist/"], "source": "src/index.ts", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "main": "dist/umd/index.production.js", "umd:main": "dist/umd/index.production.js", "unpkg": "dist/umd/index.production.js", "jsdelivr": "dist/umd/index.production.js", "scripts": {"build:clean": "rm -rf ./dist", "build:esm": "babel src --root-mode upward --extensions '.ts,.tsx' --out-dir dist/esm --ignore '**/*/__tests__/'", "build:types": "tsc -p ./tsconfig.declaration.json --outDir ./dist/esm", "build:umd": "rollup --config", "build": "yarn build:clean && yarn build:umd && yarn build:esm && yarn build:types", "on:change": "concurrently \"yarn build:esm\" \"yarn build:types\"", "prepare": "yarn build:esm && yarn build:types", "watch": "watch \"yarn on:change\" --ignoreDirectoryPattern \"/dist/\""}, "dependencies": {"@algolia/autocomplete-shared": "1.9.3"}, "devDependencies": {"algoliasearch": "4.16.0"}, "peerDependencies": {"@algolia/client-search": ">= 4.9.1 < 6", "algoliasearch": ">= 4.9.1 < 6"}}