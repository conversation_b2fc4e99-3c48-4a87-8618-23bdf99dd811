'use strict';var a="undefined"==typeof global?"undefined"===typeof self?{}:self:global,p=String.fromCharCode,q={}.toString,r=a.SharedArrayBuffer,v=r?q.call(r):"",w=a.Uint8Array,x=w?q.call(ArrayBuffer.prototype):"",y=a.Buffer;try{!y&&a.require&&(y=a.require("Buffer"));var z=y.prototype,A=q.call(z)}catch(g){}var B=y.allocUnsafe,C=!!w&&!y,D=new Uint16Array(32),E=!y||!!w&&w.prototype.isPrototypeOf(z),G=F.prototype;function H(){}
function I(g){var h=g&&g.buffer||g,c=q.call(h);if(c!==x&&c!==A&&c!==v&&"[object ArrayBuffer]"!==c&&void 0!==g)throw TypeError("Failed to execute 'decode' on 'TextDecoder': The provided value is not of type '(ArrayBuffer or ArrayBufferView)'");g=E?new w(h):h||[];c=h="";for(var b=0,d=g.length|0,u=d-32|0,f,e,k=0,t=0,m,l=0,n=-1;b<d;){for(f=b<=u?32:d-b|0;l<f;b=b+1|0,l=l+1|0){e=g[b]&255;switch(e>>4){case 15:m=g[b=b+1|0]&255;if(2!==m>>6||247<e){b=b-1|0;break}k=(e&7)<<6|m&63;t=5;e=256;case 14:m=g[b=b+1|0]&
255,k<<=6,k|=(e&15)<<6|m&63,t=2===m>>6?t+4|0:24,e=e+256&768;case 13:case 12:m=g[b=b+1|0]&255,k<<=6,k|=(e&31)<<6|m&63,t=t+7|0,b<d&&2===m>>6&&k>>t&&1114112>k?(e=k,k=k-65536|0,0<=k&&(n=(k>>10)+55296|0,e=(k&1023)+56320|0,31>l?(D[l]=n,l=l+1|0,n=-1):(m=n,n=e,e=m))):(e>>=8,b=b-e-1|0,e=65533),k=t=0,f=b<=u?32:d-b|0;default:D[l]=e;continue;case 11:case 10:case 9:case 8:}D[l]=65533}c+=p(D[0],D[1],D[2],D[3],D[4],D[5],D[6],D[7],D[8],D[9],D[10],D[11],D[12],D[13],D[14],D[15],D[16],D[17],D[18],D[19],D[20],D[21],
D[22],D[23],D[24],D[25],D[26],D[27],D[28],D[29],D[30],D[31]);32>l&&(c=c.slice(0,l-32|0));if(b<d){if(D[0]=n,l=~n>>>31,n=-1,c.length<h.length)continue}else-1!==n&&(c+=p(n));h+=c;c=""}return h}H.prototype.decode=I;function F(){}
function J(g){g=void 0===g?"":""+g;var h=g.length|0,c=C?new w((h<<1)+8|0):B?B((h<<1)+8|0):new y((h<<1)+8|0),b,d=0,u=!1;for(b=0;b<h;b=b+1|0,d=d+1|0){var f=g.charCodeAt(b)|0;if(127>=f)c[d]=f;else{if(2047>=f)c[d]=192|f>>6;else{a:{if(55296<=f)if(56320>f){var e=g.charCodeAt(b=b+1|0)|0;if(56320<=e&&57343>=e){f=(f<<10)+e-56613888|0;if(65535<f){c[d]=240|f>>18;c[d=d+1|0]=128|f>>12&63;c[d=d+1|0]=128|f>>6&63;c[d=d+1|0]=128|f&63;continue}break a}f=65533}else 57343>=f&&(f=65533);!u&&b<<1<d&&b<<1<(d-7|0)&&(u=!0,
e=C?new w(3*h):B?B(3*h):new y(3*h),e.set(c),c=e)}c[d]=224|f>>12;c[d=d+1|0]=128|f>>6&63}c[d=d+1|0]=128|f&63}}return c.subarray(0,d)}G.encode=J;function K(g,h){var c=g[h];return function(){return c.apply(g,arguments)}}var L=a.TextDecoder,M=a.TextEncoder;var TextDecoder=L||H,TextEncoder=M||F,decode=L?K(new L,"decode"):I,encode=M?K(new M,"encode"):J;export{TextDecoder,TextEncoder,decode,encode};//AnonyCo
//# sourceMappingURL=https://cdn.jsdelivr.net/gh/AnonyCo/FastestSmallestTextEncoderDecoder/NodeJS/EncoderAndDecoderNodeJS.min.mjs.map
