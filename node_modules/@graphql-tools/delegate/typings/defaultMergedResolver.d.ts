import { GraphQLResolveInfo } from 'graphql';
import { ExternalObject } from './types.js';
/**
 * Resolver that knows how to:
 * a) handle aliases for proxied schemas
 * b) handle errors from proxied schemas
 * c) handle external to internal enum conversion
 */
export declare function defaultMergedResolver(parent: ExternalObject, args: Record<string, any>, context: Record<string, any>, info: GraphQLResolveInfo): any;
