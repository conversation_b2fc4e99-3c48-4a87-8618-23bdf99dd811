{"name": "@graphql-tools/delegate", "version": "9.0.35", "description": "A set of utils for faster development of GraphQL tools", "sideEffects": false, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}, "dependencies": {"@graphql-tools/batch-execute": "^8.5.22", "@graphql-tools/executor": "^0.0.20", "@graphql-tools/schema": "^9.0.19", "@graphql-tools/utils": "^9.2.1", "dataloader": "^2.2.2", "tslib": "^2.5.0", "value-or-promise": "^1.0.12"}, "repository": {"type": "git", "url": "ardatan/graphql-tools", "directory": "packages/delegate"}, "license": "MIT", "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./*": {"require": {"types": "./typings/*.d.cts", "default": "./cjs/*.js"}, "import": {"types": "./typings/*.d.ts", "default": "./esm/*.js"}, "default": {"types": "./typings/*.d.ts", "default": "./esm/*.js"}}, "./package.json": "./package.json"}}