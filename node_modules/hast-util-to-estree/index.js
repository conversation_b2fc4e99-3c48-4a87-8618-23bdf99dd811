/**
 * @typedef {import('./lib/state.js').ElementAttributeNameCase} ElementAttributeNameCase
 * @typedef {import('./lib/state.js').Handle} Handle
 * @typedef {import('./lib/state.js').Options} Options
 * @typedef {import('./lib/state.js').Space} Space
 * @typedef {import('./lib/state.js').State} State
 * @typedef {import('./lib/state.js').StylePropertyNameCase} StylePropertyNameCase
 */

export {handlers as defaultHandlers} from './lib/handlers/index.js'
export {toEstree} from './lib/index.js'
