{"name": "@apollographql/graphql-playground-html", "version": "1.6.29", "homepage": "https://github.com/graphcool/graphql-playground/tree/master/packages/graphql-playground-html", "description": "GraphQL IDE for better development workflows (GraphQL Subscriptions, interactive docs & collaboration).", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <johanne<PERSON>@graph.cool>", "<PERSON> <<EMAIL>>"], "repository": "http://github.com/graphcool/graphql-playground.git", "license": "MIT", "main": "dist/index.js", "files": ["dist"], "scripts": {"build": "rimraf dist && tsc", "prepare": "npm run build"}, "devDependencies": {"@types/node": "12.12.34", "rimraf": "3.0.2", "typescript": "3.9.5"}, "typings": "dist/index.d.ts", "typescript": {"definition": "dist/index.d.ts"}, "dependencies": {"xss": "^1.0.8"}}