{"name": "@apollographql/apollo-tools", "version": "0.5.4", "author": "Apollo GraphQL <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/apollographql/apollo-tooling.git"}, "homepage": "https://github.com/apollographql/apollo-tooling", "bugs": "https://github.com/apollographql/apollo-tooling/issues", "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=8", "npm": ">=6"}, "peerDependencies": {"graphql": "^14.2.1 || ^15.0.0 || ^16.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testMatch": null, "testRegex": "/__tests__/.*\\.test\\.(js|ts)$", "testPathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/lib/"], "moduleFileExtensions": ["ts", "js"], "transformIgnorePatterns": ["/node_modules/"], "snapshotSerializers": ["<rootDir>/src/__tests__/snapshotSerializers/astSerializer.ts", "<rootDir>/src/__tests__/snapshotSerializers/graphQLTypeSerializer.ts"], "globals": {"ts-jest": {"tsconfig": "<rootDir>/tsconfig.test.json", "diagnostics": false}}}, "gitHead": "58b96377de23b35f31264fda805d967a63a800c7"}