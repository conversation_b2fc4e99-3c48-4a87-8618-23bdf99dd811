
Node.js (node-pkg)
==================
Total: 25 (HIGH: 18, CRITICAL: 7)

┌─────────────────────────────────────────────┬────────────────┬──────────┬──────────┬───────────────────┬────────────────────────────────────┬──────────────────────────────────────────────────────────────┐
│                   Library                   │ Vulnerability  │ Severity │  Status  │ Installed Version │           Fixed Version            │                            Title                             │
├─────────────────────────────────────────────┼────────────────┼──────────┼──────────┼───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ @nestjs/devtools-integration (package.json) │ CVE-2025-54782 │ CRITICAL │ fixed    │ 0.1.6             │ 0.2.1                              │ @nestjs/devtools-integration: CSRF to Sandbox Escape Allows  │
│                                             │                │          │          │                   │                                    │ for RCE against JS Developers                                │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-54782                   │
├─────────────────────────────────────────────┼────────────────┤          │          ├───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ @node-saml/node-saml (package.json)         │ CVE-2025-54419 │          │          │ 5.0.0             │ 5.1.0                              │ @node-saml/node-saml: Node-SAML Signature Verification       │
│                                             │                │          │          │                   │                                    │ Vulnerability                                                │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-54419                   │
├─────────────────────────────────────────────┤                │          │          │                   │                                    │                                                              │
│ @node-saml/passport-saml (package.json)     │                │          │          │                   │                                    │                                                              │
│                                             │                │          │          │                   │                                    │                                                              │
│                                             │                │          │          │                   │                                    │                                                              │
├─────────────────────────────────────────────┼────────────────┼──────────┤          ├───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ axios (package.json)                        │ CVE-2024-39338 │ HIGH     │          │ 1.7.3             │ 1.7.4                              │ axios: axios: Server-Side Request Forgery                    │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2024-39338                   │
│                                             ├────────────────┤          │          │                   ├────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│                                             │ CVE-2025-27152 │          │          │                   │ 1.8.2, 0.30.0                      │ axios: Possible SSRF and Credential Leakage via Absolute URL │
│                                             │                │          │          │                   │                                    │ in axios Requests...                                         │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-27152                   │
│                                             ├────────────────┤          │          │                   ├────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│                                             │ CVE-2025-58754 │          │          │                   │ 1.12.0                             │ axios: Axios DoS via lack of data size check                 │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-58754                   │
├─────────────────────────────────────────────┼────────────────┤          │          ├───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ body-parser (package.json)                  │ CVE-2024-45590 │          │          │ 1.20.1            │ 1.20.3                             │ body-parser: Denial of Service Vulnerability in body-parser  │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2024-45590                   │
│                                             │                │          │          ├───────────────────┤                                    │                                                              │
│                                             │                │          │          │ 1.20.2            │                                    │                                                              │
│                                             │                │          │          │                   │                                    │                                                              │
├─────────────────────────────────────────────┼────────────────┤          │          ├───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ cross-spawn (package.json)                  │ CVE-2024-21538 │          │          │ 7.0.3             │ 7.0.5, 6.0.6                       │ cross-spawn: regular expression denial of service            │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2024-21538                   │
│                                             │                │          │          │                   │                                    │                                                              │
│                                             │                │          │          │                   │                                    │                                                              │
│                                             │                │          │          │                   │                                    │                                                              │
├─────────────────────────────────────────────┼────────────────┤          ├──────────┼───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ dicer (package.json)                        │ CVE-2022-24434 │          │ affected │ 0.3.0             │                                    │ dicer: nodejs service crash by sending a crafted payload     │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2022-24434                   │
├─────────────────────────────────────────────┼────────────────┤          ├──────────┼───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ dset (package.json)                         │ CVE-2024-21529 │          │ fixed    │ 3.1.3             │ 3.1.4                              │ dset: Prototype Pollution                                    │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2024-21529                   │
├─────────────────────────────────────────────┼────────────────┼──────────┤          ├───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ form-data (package.json)                    │ CVE-2025-7783  │ CRITICAL │          │ 4.0.0             │ 2.5.4, 3.0.4, 4.0.4                │ form-data: Unsafe random function in form-data               │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-7783                    │
├─────────────────────────────────────────────┼────────────────┼──────────┤          ├───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ multer (package.json)                       │ CVE-2025-47935 │ HIGH     │          │ 1.4.4-lts.1       │ 2.0.0                              │ Multer vulnerable to Denial of Service via memory leaks from │
│                                             │                │          │          │                   │                                    │ unclosed streams...                                          │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-47935                   │
│                                             ├────────────────┤          │          │                   │                                    ├──────────────────────────────────────────────────────────────┤
│                                             │ CVE-2025-47944 │          │          │                   │                                    │ Multer vulnerable to Denial of Service from maliciously      │
│                                             │                │          │          │                   │                                    │ crafted requests                                             │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-47944                   │
│                                             ├────────────────┤          │          │                   ├────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│                                             │ CVE-2025-48997 │          │          │                   │ 2.0.1                              │ multer: Multer vulnerable to Denial of Service via unhandled │
│                                             │                │          │          │                   │                                    │ exception                                                    │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-48997                   │
│                                             ├────────────────┤          │          │                   ├────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│                                             │ CVE-2025-7338  │          │          │                   │ 2.0.2                              │ multer: Multer Denial of Service                             │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-7338                    │
├─────────────────────────────────────────────┼────────────────┤          │          ├───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ path-to-regexp (package.json)               │ CVE-2024-45296 │          │          │ 0.1.7             │ 1.9.0, 0.1.10, 8.0.0, 3.3.0, 6.3.0 │ path-to-regexp: Backtracking regular expressions cause ReDoS │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2024-45296                   │
│                                             ├────────────────┤          │          │                   ├────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│                                             │ CVE-2024-52798 │          │          │                   │ 0.1.12                             │ path-to-regexp: path-to-regexp Unpatched `path-to-regexp`    │
│                                             │                │          │          │                   │                                    │ ReDoS in 0.1.x                                               │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2024-52798                   │
│                                             ├────────────────┤          │          ├───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│                                             │ CVE-2024-45296 │          │          │ 0.2.5             │ 1.9.0, 0.1.10, 8.0.0, 3.3.0, 6.3.0 │ path-to-regexp: Backtracking regular expressions cause ReDoS │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2024-45296                   │
│                                             │                │          │          ├───────────────────┤                                    │                                                              │
│                                             │                │          │          │ 3.2.0             │                                    │                                                              │
│                                             │                │          │          │                   │                                    │                                                              │
├─────────────────────────────────────────────┼────────────────┼──────────┤          ├───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ sha.js (package.json)                       │ CVE-2025-9288  │ CRITICAL │          │ 2.4.11            │ 2.4.12                             │ sha.js: Missing type checks leading to hash rewind and       │
│                                             │                │          │          │                   │                                    │ passing on crafted...                                        │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-9288                    │
├─────────────────────────────────────────────┼────────────────┼──────────┤          ├───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ ws (package.json)                           │ CVE-2024-37890 │ HIGH     │          │ 8.16.0            │ 5.2.4, 6.2.3, 7.5.10, 8.17.1       │ nodejs-ws: denial of service when handling a request with    │
│                                             │                │          │          │                   │                                    │ many HTTP headers...                                         │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2024-37890                   │
├─────────────────────────────────────────────┼────────────────┼──────────┤          ├───────────────────┼────────────────────────────────────┼──────────────────────────────────────────────────────────────┤
│ xml-crypto (package.json)                   │ CVE-2025-29774 │ CRITICAL │          │ 6.0.0             │ 6.0.1, 3.2.1, 2.1.6                │ xml-crypto: xml-crypto Vulnerable to XML Signature           │
│                                             │                │          │          │                   │                                    │ Verification Bypass via Multiple SignedInfo References...    │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-29774                   │
│                                             ├────────────────┤          │          │                   │                                    ├──────────────────────────────────────────────────────────────┤
│                                             │ CVE-2025-29775 │          │          │                   │                                    │ xml-crypto: xml-crypto Vulnerable to XML Signature           │
│                                             │                │          │          │                   │                                    │ Verification Bypass via DigestValue Comment                  │
│                                             │                │          │          │                   │                                    │ https://avd.aquasec.com/nvd/cve-2025-29775                   │ode.js (node-pkg)
==================
