{"dependencies": {"@air/react-drag-to-select": "^5.0.8", "@apollo/client": "^3.7.17", "@apollo/server": "^4.7.3", "@aws-sdk/client-lambda": "^3.614.0", "@aws-sdk/client-s3": "^3.363.0", "@aws-sdk/credential-providers": "^3.363.0", "@azure/storage-blob": "^12.27.0", "@blocknote/mantine": "^0.22.0", "@blocknote/react": "^0.22.0", "@codesandbox/sandpack-react": "^2.13.5", "@dagrejs/dagre": "^1.1.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@envelop/on-resolve": "^4.1.0", "@floating-ui/react": "^0.24.3", "@fullcalendar/core": "^6.1.18", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/react": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@google-cloud/local-auth": "2.1.0", "@graphiql/plugin-explorer": "^1.0.2", "@graphql-tools/schema": "^10.0.0", "@hello-pangea/dnd": "^16.2.0", "@hookform/resolvers": "^3.1.1", "@jsdevtools/rehype-toc": "^3.0.2", "@linaria/core": "^6.2.0", "@linaria/react": "^6.2.1", "@lingui/core": "^5.1.2", "@lingui/react": "^5.1.2", "@mdx-js/react": "^3.0.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@nestjs/apollo": "^11.0.5", "@nestjs/axios": "^3.0.1", "@nestjs/cli": "^9.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.2", "@nestjs/core": "^9.0.0", "@nestjs/devtools-integration": "^0.2.1", "@nestjs/event-emitter": "^2.0.3", "@nestjs/jwt": "^10.0.3", "@nestjs/passport": "^9.0.3", "@nestjs/platform-express": "^9.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^4.0.1", "@nestjs/terminus": "^9.2.2", "@nestjs/typeorm": "^10.0.0", "@nx/eslint-plugin": "^17.2.8", "@octokit/graphql": "^7.0.2", "@ptc-org/nestjs-query-core": "^4.2.0", "@ptc-org/nestjs-query-typeorm": "4.2.1-alpha.2", "@react-email/components": "0.0.12", "@react-email/render": "0.0.10", "@react-pdf/renderer": "^4.3.0", "@sentry/node": "^8", "@sentry/profiling-node": "^8", "@sentry/react": "^8", "@sniptt/guards": "^0.2.0", "@stoplight/elements": "^8.0.5", "@swc/jest": "^0.2.29", "@tabler/icons-react": "^2.44.0", "@types/dompurify": "^3.0.5", "@types/facepaint": "^1.2.5", "@types/lodash.camelcase": "^4.3.7", "@types/lodash.chunk": "^4.2.9", "@types/lodash.merge": "^4.6.7", "@types/lodash.pick": "^4.3.7", "@types/nodemailer": "^6.4.14", "@types/passport-microsoft": "^1.0.3", "@wyw-in-js/vite": "^0.5.3", "@xyflow/react": "^12.3.5", "add": "^2.0.6", "addressparser": "^1.0.1", "afterframe": "^1.0.2", "apollo-server-express": "^3.12.0", "apollo-upload-client": "^17.0.0", "archiver": "^7.0.1", "axios": "^1.8.2", "bcrypt": "^5.1.1", "better-sqlite3": "^9.2.2", "body-parser": "^1.20.3", "bullmq": "^4.15.0", "bytes": "^3.1.2", "class-transformer": "^0.5.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "danger-plugin-todos": "^1.3.1", "dataloader": "^2.2.2", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "debounce": "^2.0.0", "deep-equal": "^2.2.2", "dompurify": "^3.0.11", "dotenv-cli": "^7.2.1", "drizzle-orm": "^0.29.3", "esbuild-plugin-svgr": "^2.1.0", "facepaint": "^1.2.1", "file-type": "16.5.4", "form-data": "^4.0.4", "framer-motion": "^11.18.0", "fuse.js": "^7.1.0", "googleapis": "105", "graphiql": "^3.1.1", "graphql": "16.8.0", "graphql-fields": "^2.0.3", "graphql-middleware": "^6.1.35", "graphql-rate-limit": "^3.3.0", "graphql-scalars": "^1.23.0", "graphql-subscriptions": "2.0.0", "graphql-tag": "^2.12.6", "graphql-type-json": "^0.3.2", "graphql-upload": "^13.0.0", "graphql-yoga": "^4.0.4", "hex-rgb": "^5.0.0", "iframe-resizer-react": "^1.1.0", "immer": "^10.0.2", "jest-mock-extended": "^3.0.4", "js-cookie": "^3.0.5", "js-levenshtein": "^1.1.6", "json-2-csv": "^5.4.0", "jsonwebtoken": "^9.0.0", "libphonenumber-js": "^1.10.26", "lodash.camelcase": "^4.3.0", "lodash.chunk": "^4.2.0", "lodash.compact": "^3.0.1", "lodash.debounce": "^4.0.8", "lodash.escaperegexp": "^4.1.2", "lodash.groupby": "^4.6.0", "lodash.identity": "^3.0.0", "lodash.isempty": "^4.4.0", "lodash.isequal": "^4.5.0", "lodash.isobject": "^3.0.2", "lodash.kebabcase": "^4.1.1", "lodash.mapvalues": "^4.6.0", "lodash.merge": "^4.6.2", "lodash.omit": "^4.5.0", "lodash.pick": "^4.4.0", "lodash.pickby": "^4.6.0", "lodash.snakecase": "^4.1.1", "lodash.upperfirst": "^4.3.1", "luxon": "^3.3.0", "microdiff": "^1.3.2", "moize": "^6.1.6", "nest-commander": "^3.12.0", "next": "14.0.4", "next-mdx-remote": "^4.4.1", "nodemailer": "^6.9.8", "openapi-types": "^12.1.3", "overlayscrollbars": "^2.6.1", "overlayscrollbars-react": "^0.5.4", "papaparse": "^5.5.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-microsoft": "^2.0.0", "patch-package": "^8.0.0", "pg": "^8.11.3", "pg-boss": "^9.0.3", "planer": "^1.2.0", "pluralize": "^8.0.0", "prettier": "^3.0.3", "prism-react-renderer": "^2.1.0", "qs": "^6.11.2", "react": "^18.2.0", "react-csv": "^2.2.2", "react-data-grid": "7.0.0-beta.13", "react-datepicker": "^6.7.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.11", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.45.1", "react-hotkeys-hook": "^4.4.4", "react-icons": "^4.12.0", "react-imask": "^7.6.0", "react-intersection-observer": "^9.5.2", "react-loading-skeleton": "^3.3.1", "react-phone-number-input": "^3.3.4", "react-responsive": "^9.0.2", "react-router-dom": "^6.4.4", "react-textarea-autosize": "^8.4.1", "react-to-print": "^3.1.1", "react-tooltip": "^5.13.1", "recharts": "^3.1.2", "recoil": "^0.7.7", "rehype-slug": "^6.0.0", "remark-behead": "^3.1.0", "remark-gfm": "^3.0.1", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "scroll-into-view": "^1.16.2", "semver": "^7.5.4", "sharp": "^0.33.5", "slash": "^5.1.0", "stripe": "^17.3.1", "ts-key-enum": "^2.0.12", "tslib": "^2.3.0", "tsup": "^8.2.4", "type-fest": "4.10.1", "typescript": "5.3.3", "use-context-selector": "^2.0.0", "use-debounce": "^10.0.0", "uuid": "^9.0.0", "vite-tsconfig-paths": "^4.2.1", "ws": "^8.17.1", "wx-react-gantt": "^1.3.1", "xlsx-ugnis": "^0.19.3", "zod": "3.23.8"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.24.6", "@crxjs/vite-plugin": "^1.0.14", "@graphql-codegen/cli": "^3.3.1", "@graphql-codegen/client-preset": "^4.1.0", "@graphql-codegen/typescript": "^3.0.4", "@graphql-codegen/typescript-operations": "^3.0.4", "@graphql-codegen/typescript-react-apollo": "^3.3.7", "@lingui/cli": "^5.1.2", "@lingui/swc-plugin": "^5.0.2", "@lingui/vite-plugin": "^5.1.2", "@microsoft/microsoft-graph-types": "^2.40.0", "@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@next/eslint-plugin-next": "^14.1.4", "@nx/eslint": "18.3.3", "@nx/eslint-plugin": "18.3.3", "@nx/jest": "18.3.3", "@nx/js": "18.3.3", "@nx/react": "18.3.3", "@nx/storybook": "18.3.3", "@nx/vite": "18.3.3", "@nx/web": "18.3.3", "@playwright/test": "^1.46.0", "@sentry/types": "^7.109.0", "@storybook/addon-actions": "^7.6.3", "@storybook/addon-coverage": "^1.0.0", "@storybook/addon-essentials": "^7.6.7", "@storybook/addon-interactions": "^7.6.7", "@storybook/addon-links": "^7.6.7", "@storybook/addon-onboarding": "^1.0.10", "@storybook/blocks": "^7.6.3", "@storybook/core-server": "7.6.3", "@storybook/icons": "^1.2.9", "@storybook/jest": "^0.2.3", "@storybook/react": "^7.6.3", "@storybook/react-vite": "^7.6.3", "@storybook/test": "^7.6.3", "@storybook/test-runner": "^0.16.0", "@storybook/testing-library": "^0.2.2", "@stylistic/eslint-plugin": "^1.5.0", "@swc-node/register": "1.8.0", "@swc/cli": "^0.3.12", "@swc/core": "~1.3.100", "@swc/helpers": "~0.5.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "14.0.0", "@types/addressparser": "^1.0.3", "@types/apollo-upload-client": "^17.0.2", "@types/bcrypt": "^5.0.0", "@types/better-sqlite3": "^7.6.8", "@types/bytes": "^3.1.1", "@types/chrome": "^0.0.267", "@types/deep-equal": "^1.0.1", "@types/express": "^4.17.13", "@types/file-saver": "^2.0.7", "@types/graphql-fields": "^1.3.6", "@types/graphql-upload": "^8.0.12", "@types/js-cookie": "^3.0.3", "@types/js-levenshtein": "^1.1.3", "@types/lodash.camelcase": "^4.3.7", "@types/lodash.compact": "^3.0.9", "@types/lodash.debounce": "^4.0.7", "@types/lodash.escaperegexp": "^4.1.9", "@types/lodash.groupby": "^4.6.9", "@types/lodash.identity": "^3.0.9", "@types/lodash.isempty": "^4.4.7", "@types/lodash.isequal": "^4.5.7", "@types/lodash.isobject": "^3.0.7", "@types/lodash.kebabcase": "^4.1.7", "@types/lodash.mapvalues": "^4.6.9", "@types/lodash.omit": "^4.5.9", "@types/lodash.pickby": "^4.6.9", "@types/lodash.snakecase": "^4.1.7", "@types/lodash.upperfirst": "^4.3.7", "@types/luxon": "^3.3.0", "@types/ms": "^0.7.31", "@types/node": "18.19.26", "@types/passport-google-oauth20": "^2.0.11", "@types/passport-jwt": "^3.0.8", "@types/pluralize": "^0.0.33", "@types/react": "^18.2.39", "@types/react-csv": "^1.1.10", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18.2.15", "@types/scroll-into-view": "^1.16.0", "@types/supertest": "^2.0.11", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/experimental-utils": "^5.62.0", "@typescript-eslint/parser": "6.21.0", "@typescript-eslint/utils": "6.21.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/ui": "1.4.0", "chromatic": "^6.18.0", "concurrently": "^8.2.2", "cross-var": "^1.1.0", "danger": "^11.3.0", "dotenv-cli": "^7.2.1", "drizzle-kit": "^0.20.14", "esbuild": "^0.23.0", "eslint": "^8.53.0", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-lingui": "^0.9.0", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-project-structure": "^3.9.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-storybook": "^0.6.15", "eslint-plugin-unicorn": "^51.0.1", "eslint-plugin-unused-imports": "^3.0.0", "http-server": "^14.1.1", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-environment-node": "^29.4.1", "jest-fetch-mock": "^3.0.3", "jsdom": "~22.1.0", "msw": "^2.0.11", "msw-storybook-addon": "2.0.0--canary.122.b3ed3b1.0", "nx": "18.3.3", "playwright": "^1.46.0", "prettier": "^3.1.1", "raw-loader": "^4.0.2", "rimraf": "^5.0.5", "source-map-support": "^0.5.20", "storybook": "^7.6.3", "storybook-addon-cookie": "^3.2.0", "storybook-addon-pseudo-states": "^2.1.2", "storybook-dark-mode": "^3.0.3", "supertest": "^6.1.3", "ts-jest": "^29.1.1", "ts-loader": "^9.2.3", "ts-node": "10.9.1", "tsconfig-paths": "^4.2.0", "tsx": "^4.17.0", "vite": "^5.4.0", "vite-plugin-checker": "^0.6.2", "vite-plugin-dts": "3.8.1", "vite-plugin-svgr": "^4.2.0", "vitest": "1.4.0"}, "engines": {"node": "^18.17.1", "npm": "please-use-yarn", "yarn": ">=4.0.2"}, "license": "AGPL-3.0", "name": "twenty", "packageManager": "yarn@4.4.0", "resolutions": {"graphql": "16.8.0", "type-fest": "4.10.1", "typescript": "5.3.3", "prosemirror-model": "1.23.0"}, "version": "0.2.1", "nx": {}, "scripts": {"start": "npx concurrently --kill-others 'npx nx run-many -t start -p twenty-server twenty-front' 'npx wait-on tcp:3000 && npx nx run twenty-server:worker'"}, "workspaces": {"packages": ["packages/twenty-chrome-extension", "packages/twenty-front", "packages/twenty-server", "packages/twenty-emails", "packages/twenty-ui", "packages/twenty-utils", "packages/twenty-zapier", "packages/twenty-website", "packages/twenty-e2e-testing", "packages/twenty-shared", "tools/eslint-rules"]}}