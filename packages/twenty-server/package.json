{"name": "twenty-server", "version": "0.41.0-canary", "description": "", "author": "", "private": true, "license": "AGPL-3.0", "scripts": {"nx": "NX_DEFAULT_PROJECT=twenty-server node ../../node_modules/nx/bin/nx.js", "start:prod": "node dist/src/main", "command:prod": "node dist/src/command/command", "worker:prod": "node dist/src/queue-worker/queue-worker", "database:init:prod": "npx ts-node ./scripts/setup-db.ts && yarn database:migrate:prod", "database:migrate:prod": "npx -y typeorm migration:run -d dist/src/database/typeorm/metadata/metadata.datasource && npx -y typeorm migration:run -d dist/src/database/typeorm/core/core.datasource", "typeorm": "../../node_modules/typeorm/.bin/typeorm"}, "dependencies": {"@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@graphql-yoga/nestjs": "patch:@graphql-yoga/nestjs@2.1.0#./patches/@graphql-yoga+nestjs+2.1.0.patch", "@langchain/mistralai": "^0.0.24", "@langchain/openai": "^0.1.3", "@monaco-editor/react": "^4.6.0", "@nestjs/cache-manager": "^2.2.1", "@nestjs/devtools-integration": "^0.2.1", "@nestjs/graphql": "patch:@nestjs/graphql@12.1.1#./patches/@nestjs+graphql+12.1.1.patch", "@node-saml/passport-saml": "^5.1.0", "@ptc-org/nestjs-query-graphql": "patch:@ptc-org/nestjs-query-graphql@4.2.0#./patches/@ptc-org+nestjs-query-graphql+4.2.0.patch", "@revertdotdev/revert-react": "^0.0.21", "@sentry/nestjs": "^8.30.0", "cache-manager": "^5.4.0", "cache-manager-redis-yet": "^4.1.2", "class-validator": "patch:class-validator@0.14.0#./patches/class-validator+0.14.0.patch", "connect-redis": "^7.1.1", "express-session": "^1.18.1", "graphql-middleware": "^6.1.35", "handlebars": "^4.7.8", "jsdom": "~22.1.0", "jwt-decode": "^4.0.0", "langchain": "^0.2.6", "langfuse-langchain": "^3.11.2", "lodash.differencewith": "^4.5.0", "lodash.merge": "^4.6.2", "lodash.omitby": "^4.6.0", "lodash.uniq": "^4.5.0", "lodash.uniqby": "^4.7.0", "monaco-editor": "^0.51.0", "monaco-editor-auto-typings": "^0.4.5", "openid-client": "^5.7.0", "passport": "^0.7.0", "psl": "^1.9.0", "redis": "^4.7.0", "ts-morph": "^24.0.0", "tsconfig-paths": "^4.2.0", "typeorm": "patch:typeorm@0.3.20#./patches/typeorm+0.3.20.patch", "unzipper": "^0.12.3", "zod-to-json-schema": "^3.23.1"}, "devDependencies": {"@nestjs/cli": "10.3.0", "@nx/js": "18.3.3", "@types/express-session": "^1.18.0", "@types/lodash.differencewith": "^4.5.9", "@types/lodash.isempty": "^4.4.7", "@types/lodash.isequal": "^4.5.8", "@types/lodash.isobject": "^3.0.7", "@types/lodash.merge": "^4.6.9", "@types/lodash.omit": "^4.5.9", "@types/lodash.omitby": "^4.6.9", "@types/lodash.snakecase": "^4.1.7", "@types/lodash.uniq": "^4.5.9", "@types/lodash.uniqby": "^4.7.9", "@types/lodash.upperfirst": "^4.3.7", "@types/openid-client": "^3.7.0", "@types/react": "^18.2.39", "@types/unzipper": "^0", "rimraf": "^5.0.5", "twenty-emails": "workspace:*", "twenty-shared": "workspace:*", "typescript": "5.3.3"}, "engines": {"node": "^18.17.1", "npm": "please-use-yarn", "yarn": "^4.0.2"}}