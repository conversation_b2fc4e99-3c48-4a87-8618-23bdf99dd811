{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "NODE_OPTIONS=--max_old_space_size=4096 nuxt build", "dev": "export NODE_TLS_REJECT_UNAUTHORIZED=0; nuxt dev", "docker-dev": "export NODE_TLS_REJECT_UNAUTHORIZED=0; nuxt dev --host 0.0.0.0", "generate": "export NODE_TLS_REJECT_UNAUTHORIZED=0; nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint --ext .js,.vue .", "lint:fix": "eslint . --fix --ext .js,.vue"}, "devDependencies": {"@iconify-json/clarity": "^1.2.1", "@iconify-json/ic": "^1.2.1", "@iconify-json/octicon": "^1.2.1", "@nuxt/devtools": "^1.6.1", "@nuxt/eslint-config": "^0.2.0", "@nuxt/icon": "^1.8.2", "@nuxtjs/i18n": "^9.0.0", "@nuxtjs/sitemap": "^6.1.3", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "nuxt": "^3.16.1", "nuxt-utm": "^0.2.2", "postcss": "^8.4.47", "prettier": "^3.3.3", "sass": "^1.80.2", "vue": "^3.5.13", "vue-router": "^4.4.5"}, "dependencies": {"@codemirror/lang-html": "^6.4.9", "@iconify-json/material-symbols": "^1.2.4", "@nuxt/ui": "^2.19.2", "@pinia/nuxt": "^0.5.5", "@popperjs/core": "^2.11.8", "@stripe/stripe-js": "^5.5.0", "@sentry/nuxt": "^9.8.0", "@sentry/vite-plugin": "^3.2.2", "@sentry/vue": "^9.8.0", "@vueuse/components": "^11.2.0", "@vueuse/core": "^11.2.0", "@vueuse/integrations": "^11.2.0", "@vueuse/motion": "^2.2.6", "@vueuse/nuxt": "^11.2.0", "amplitude-js": "^8.21.9", "chart.js": "^4.4.5", "clone-deep": "^4.0.1", "codemirror": "^6.0.1", "crisp-sdk-web": "^1.0.25", "date-fns": "^2.30.0", "debounce": "^1.2.1", "esbuild": "^0.23.1", "fuse.js": "^7.0.0", "js-sha256": "^0.10.1", "libphonenumber-js": "^1.11.12", "object-to-formdata": "^4.5.1", "pinia": "^2.2.4", "prismjs": "^1.29.0", "qrcode": "^1.5.4", "quagga": "^0.12.1", "query-builder-vue-3": "^1.0.1", "quill": "^2.0.2", "tailwind-merge": "^2.5.4", "tinymotion": "^0.2.0", "v-calendar": "3.0.1", "vue-chartjs": "^5.3.2", "vue-codemirror": "^6.1.1", "vue-confetti": "^2.3.0", "vue-country-flag-next": "^2.3.2", "vue-json-pretty": "^2.4.0", "vue-notion": "^3.0.0", "vue-signature-pad": "^3.0.2", "vue-stripe-js": "^1.0.4", "vuedraggable": "next", "webcam-easy": "^1.1.1"}, "eslintIgnore": ["/lib/jaro-winkler.js"], "overrides": {"@vercel/nft": "^0.27.4", "form-data": "^2.5.4", "cross-spawn": "^7.0.6"}}