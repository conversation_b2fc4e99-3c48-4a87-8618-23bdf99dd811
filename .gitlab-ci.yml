include:
  - template: Jobs/SAST.gitlab-ci.yml

.docker-tmpl:
  image: docker:latest
  services:
    - docker:dind

.install-trivy:
  before_script:
    - export TRIVY_VERSION=$(wget -qO - "https://api.github.com/repos/aquasecurity/trivy/releases/latest" | grep '"tag_name":' | sed -E 's/.*"v([^"]+)".*/\1/')
    - wget --no-verbose https://github.com/aquasecurity/trivy/releases/download/v${TRIVY_VERSION}/trivy_${TRIVY_VERSION}_Linux-64bit.tar.gz -O - | tar -zxvf -
    - wget -O gitlab.tpl https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/gitlab.tpl
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY

stages:
  - build
  - test
  - security-scan
  # - container-scan
  - dependency-scan
  - deploy 

build-image:
  extends: .docker-tmpl
  stage: build
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build --pull -t "$CI_REGISTRY/$CI_APP_IMAGE:latest" -t "$CI_REGISTRY/$CI_APP_IMAGE:$CI_COMMIT_TAG" -f ./packages/twenty-docker/twenty/Dockerfile .
    - docker push --all-tags "$CI_REGISTRY/$CI_APP_IMAGE"
    - docker build --pull -t "$CI_REGISTRY/$CI_HONDA_SERVER_IMAGE:latest" -t "$CI_REGISTRY/$CI_HONDA_SERVER_IMAGE:$CI_COMMIT_TAG" -f ./packages/honda-server/Dockerfile .
    - docker push --all-tags "$CI_REGISTRY/$CI_HONDA_SERVER_IMAGE"
    - docker build --pull -t "$CI_REGISTRY/$CI_HONDA_FORM_CLIENT_IMAGE:latest" -t "$CI_REGISTRY/$CI_HONDA_FORM_CLIENT_IMAGE:$CI_COMMIT_TAG" -f ./packages/form-client/Dockerfile ./packages/form-client/
    - docker push --all-tags "$CI_REGISTRY/$CI_HONDA_FORM_CLIENT_IMAGE"
    - docker rmi -f "$CI_REGISTRY/$CI_APP_IMAGE:latest" "$CI_REGISTRY/$CI_HONDA_SERVER_IMAGE:latest" "$CI_REGISTRY/$CI_HONDA_FORM_CLIENT_IMAGE:latest"
  rules:
    - if: "$CI_COMMIT_TAG"

semgrep-sast:
  stage: security-scan
  rules:
    - if: "$CI_COMMIT_TAG"

# .container_scanning_template:
#   image: docker:stable
#   extends:
#     - .install-trivy
#   services:
#     - name: docker:dind
#       entrypoint: ["env", "-u", "DOCKER_HOST"]
#       command: ["dockerd-entrypoint.sh"]
#   stage: container-scan
#   variables:
#     CS_IMAGE_NAME: "" # This will be set by the matrix
#   script:
#     - echo "Scanning $CI_REGISTRY/${CS_IMAGE_NAME}:$CI_COMMIT_TAG"
#     - docker pull $CI_REGISTRY/${CS_IMAGE_NAME}:$CI_COMMIT_TAG || true
#     - >
#       ./trivy --cache-dir .trivycache/ image
#       --format template --template "@gitlab.tpl"
#       --output gl-container-scanning-report-${CS_IMAGE_NAME}.json
#       --vuln-type os
#       $CI_REGISTRY/${CS_IMAGE_NAME}:$CI_COMMIT_TAG
#     - ./trivy image --exit-code 1 --vuln-type os --severity CRITICAL,HIGH $CI_REGISTRY/${CS_IMAGE_NAME}:$CI_COMMIT_TAG
#     - docker rmi $CI_REGISTRY/${CS_IMAGE_NAME}:$CI_COMMIT_TAG
#   cache:
#     paths:
#       - .trivycache/
#   artifacts:
#     reports:
#       container_scanning: gl-container-scanning-report-*.json

# container_scanning_all_apps:
#   extends: .container_scanning_template
#   stage: container-scan
#   parallel:
#     matrix:
#       - CS_IMAGE_NAME:
#           - "crm-server"
#           - "honda-form-client"
#           - "honda-server"

.dependency_scanning_template:
  image: docker:stable
  extends:
    - .install-trivy
  services:
    - name: docker:dind
      entrypoint: ["env", "-u", "DOCKER_HOST"]
      command: ["dockerd-entrypoint.sh"]
  stage: dependency-scan
  variables:
    CS_IMAGE_NAME: "" # This variable will be set by the parallel:matrix in the extending job
  script:
    - echo "Performing dependency scan for image $CI_REGISTRY/${CS_IMAGE_NAME}:$CI_COMMIT_TAG"
    - docker pull $CI_REGISTRY/${CS_IMAGE_NAME}:$CI_COMMIT_TAG || true
    - >
      ./trivy --cache-dir .trivycache/ image
      --format template --template "@gitlab.tpl"
      --output gl-dependency-scanning-report-${CS_IMAGE_NAME}.json
      --vuln-type library
      $CI_REGISTRY/${CS_IMAGE_NAME}:$CI_COMMIT_TAG
    - ./trivy image --exit-code 1 --vuln-type library --severity CRITICAL,HIGH $CI_REGISTRY/${CS_IMAGE_NAME}:$CI_COMMIT_TAG
    - docker rmi $CI_REGISTRY/${CS_IMAGE_NAME}:$CI_COMMIT_TAG

  cache:
    paths:
      - .trivycache/
  artifacts:
    reports:
      dependency_scanning: gl-dependency-scanning-report-*.json

dependency_scanning_all_apps:
  extends: .dependency_scanning_template
  stage: dependency-scan
  parallel:
    matrix:
      - CS_IMAGE_NAME:
          - "crm-server"
          - "honda-form-client"
          - "honda-server"

deploy-service:
  stage: deploy
  script:
    - ssh -tt -i ~/.ssh/$STAGING_VM_SSH_KEY $STAGING_VM_IP "az login --identity --username $AZURE_IAM; az acr login --name hondahatchq; cd /home/<USER>/crm-service; ./fetch-env.sh; docker-compose down; docker-compose up -d --pull always"
  rules:
    - if: "$CI_COMMIT_TAG"
  when: manual
  tags:
  - honda-shell

